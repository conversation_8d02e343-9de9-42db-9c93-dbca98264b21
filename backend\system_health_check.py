#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康检查脚本
"""

import sys
import os
import sqlite3
import requests
import json
from datetime import datetime
from pathlib import Path

def check_database():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    try:
        db_path = "data/file_share_system.db"
        if not os.path.exists(db_path):
            print("❌ 数据库文件不存在")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查主要表是否存在
        required_tables = [
            'users', 'shared_folders', 'shared_files', 
            'download_records', 'download_batches', 'search_index'
        ]
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table not in existing_tables:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"❌ 缺少数据表: {', '.join(missing_tables)}")
            return False
        
        # 检查用户数量
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✅ 数据库正常，用户数量: {user_count}")
        
        # 检查文件数量
        cursor.execute("SELECT COUNT(*) FROM shared_files")
        file_count = cursor.fetchone()[0]
        print(f"✅ 共享文件数量: {file_count}")
        
        # 检查下载记录
        cursor.execute("SELECT COUNT(*) FROM download_records")
        download_count = cursor.fetchone()[0]
        print(f"✅ 下载记录数量: {download_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_api_server():
    """检查API服务器状态"""
    print("\n🔍 检查API服务器状态...")
    try:
        api_port = 8086  # 默认端口

        # 检查健康状态
        response = requests.get(f"http://localhost:{api_port}/api/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API服务器正常运行在端口 {api_port}")
            print(f"   状态: {health_data.get('status', 'unknown')}")
            print(f"   版本: {health_data.get('version', 'unknown')}")
            return True
        else:
            print(f"❌ API服务器响应异常: {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到API服务器 (端口 {api_port})")
        return False
    except Exception as e:
        print(f"❌ API服务器检查失败: {e}")
        return False

def check_frontend_server():
    """检查前端服务器状态"""
    print("\n🔍 检查前端服务器状态...")
    try:
        frontend_port = 8084  # 默认端口

        # 检查前端页面
        response = requests.get(f"http://localhost:{frontend_port}/index.html", timeout=5)
        if response.status_code == 200:
            print(f"✅ 前端服务器正常运行在端口 {frontend_port}")
            return True
        else:
            print(f"❌ 前端服务器响应异常: {response.status_code}")
            return False

    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到前端服务器 (端口 {frontend_port})")
        return False
    except Exception as e:
        print(f"❌ 前端服务器检查失败: {e}")
        return False

def check_file_structure():
    """检查文件结构"""
    print("\n🔍 检查文件结构...")
    
    required_dirs = [
        "data", "logs", "temp", "config", 
        "models", "services", "api", "utils"
    ]
    
    required_files = [
        "main.py", "config/settings.py", "config/database.py",
        "init_database_sqlite.py"
    ]
    
    missing_dirs = []
    missing_files = []
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
    
    for file_name in required_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
    
    if missing_dirs:
        print(f"❌ 缺少目录: {', '.join(missing_dirs)}")
        return False
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件结构完整")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查Python依赖包...")
    
    required_packages = [
        'sqlalchemy', 'flask', 'PIL', 'yaml', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def main():
    """主函数"""
    print("🏥 系统健康检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    checks = [
        ("文件结构", check_file_structure),
        ("Python依赖", check_dependencies),
        ("数据库", check_database),
        ("API服务器", check_api_server),
        ("前端服务器", check_frontend_server),
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查出错: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("📋 检查结果汇总:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 系统健康状态良好！")
        return 0
    else:
        print("⚠️  系统存在问题，请检查上述异常项目")
        return 1

if __name__ == "__main__":
    sys.exit(main())
