<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录测试页面</title>
    <link rel="stylesheet" href="frontend/css/style.css">
    <link rel="stylesheet" href="frontend/css/download-records.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #3182ce;
            color: white;
        }
        .btn-primary:hover {
            background: #2c5aa0;
        }
        .btn-secondary {
            background: #718096;
            color: white;
        }
        .btn-secondary:hover {
            background: #4a5568;
        }
        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        #download-records-view {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-download"></i> 下载记录功能测试</h1>
            <p>测试新的下载记录显示功能</p>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testAPI()">
                    <i class="fas fa-play"></i> 测试API
                </button>
                <button class="btn btn-secondary" onclick="testUI()">
                    <i class="fas fa-eye"></i> 测试界面
                </button>
                <button class="btn btn-secondary" onclick="clearLog()">
                    <i class="fas fa-trash"></i> 清除日志
                </button>
            </div>
            
            <div class="log-container" id="log-container">
                <div>等待测试...</div>
            </div>
        </div>

        <!-- 下载记录视图容器 -->
        <div id="download-records-view"></div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="frontend/js/config.js"></script>
    <script src="frontend/js/utils.js"></script>
    <script src="frontend/js/components.js"></script>
    <script src="frontend/js/api.js"></script>
    
    <script>
        // 日志功能
        function log(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const color = {
                'info': '#63b3ed',
                'success': '#68d391',
                'error': '#fc8181',
                'warn': '#f6ad55'
            }[type] || '#e2e8f0';
            
            const logEntry = document.createElement('div');
            logEntry.style.color = color;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-container').innerHTML = '<div>日志已清除</div>';
        }

        // 测试API功能
        async function testAPI() {
            log('开始测试下载记录API...', 'info');
            
            try {
                // 检查API是否可用
                if (typeof DownloadAPI === 'undefined') {
                    throw new Error('DownloadAPI未定义');
                }
                log('✓ DownloadAPI已加载', 'success');

                // 测试获取下载记录
                log('正在调用 DownloadAPI.getDownloadRecords()...', 'info');
                const result = await DownloadAPI.getDownloadRecords(1, 10);
                log(`API响应: ${JSON.stringify(result, null, 2)}`, 'info');

                if (result && result.success) {
                    log(`✓ API调用成功，获取到 ${result.records?.length || 0} 条记录`, 'success');
                    log(`总记录数: ${result.total || 0}`, 'info');
                } else {
                    log(`✗ API调用失败: ${result?.error || '未知错误'}`, 'error');
                }

            } catch (error) {
                log(`✗ 测试失败: ${error.message}`, 'error');
                console.error('API测试失败:', error);
            }
        }

        // 测试UI功能
        async function testUI() {
            log('开始测试下载记录UI...', 'info');
            
            try {
                // 创建文件管理器实例
                const fileManager = {
                    currentDownloadPage: 1,
                    downloadPageSize: 20,
                    currentDownloadFilters: {},
                    totalDownloadPages: 1,

                    // 初始化下载记录界面
                    initDownloadRecordsInterface() {
                        log('正在初始化下载记录界面...', 'info');
                        // 这里复制前面实现的方法
                        const container = document.getElementById('download-records-view');
                        if (!container) {
                            throw new Error('找不到下载记录容器');
                        }

                        container.innerHTML = `
                            <div class="download-records-wrapper">
                                <div class="download-records-header">
                                    <div class="header-left">
                                        <h2><i class="fas fa-download"></i> 我的下载记录</h2>
                                        <div class="record-summary">
                                            <span class="summary-item">
                                                <i class="fas fa-file"></i>
                                                总下载: <span id="total-downloads">0</span>
                                            </span>
                                            <span class="summary-item">
                                                <i class="fas fa-hdd"></i>
                                                总大小: <span id="total-size">0 B</span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="header-right">
                                        <div class="download-controls">
                                            <button class="btn btn-primary" onclick="testLoadData()">
                                                <i class="fas fa-sync-alt"></i>
                                                加载数据
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="download-records-main">
                                    <div class="loading-state" id="loading-state">
                                        <div class="loading-spinner">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </div>
                                        <p>正在加载下载记录...</p>
                                    </div>
                                    <div class="empty-state hidden" id="empty-state">
                                        <div class="empty-icon">
                                            <i class="fas fa-download"></i>
                                        </div>
                                        <h3>暂无下载记录</h3>
                                        <p>您还没有下载过任何文件</p>
                                    </div>
                                    <div class="records-list hidden" id="records-list">
                                        <!-- 记录内容 -->
                                    </div>
                                </div>
                            </div>
                        `;
                        log('✓ 下载记录界面初始化完成', 'success');
                    },

                    // 显示空状态
                    showEmptyState() {
                        const states = ['loading-state', 'records-list'];
                        states.forEach(id => {
                            const el = document.getElementById(id);
                            if (el) el.classList.add('hidden');
                        });
                        const emptyState = document.getElementById('empty-state');
                        if (emptyState) emptyState.classList.remove('hidden');
                    },

                    // 显示记录
                    showRecords() {
                        const states = ['loading-state', 'empty-state'];
                        states.forEach(id => {
                            const el = document.getElementById(id);
                            if (el) el.classList.add('hidden');
                        });
                        const recordsList = document.getElementById('records-list');
                        if (recordsList) recordsList.classList.remove('hidden');
                    }
                };

                // 初始化界面
                fileManager.initDownloadRecordsInterface();
                log('✓ UI测试完成', 'success');

                // 测试显示空状态
                setTimeout(() => {
                    fileManager.showEmptyState();
                    log('✓ 空状态显示测试完成', 'success');
                }, 1000);

            } catch (error) {
                log(`✗ UI测试失败: ${error.message}`, 'error');
                console.error('UI测试失败:', error);
            }
        }

        // 测试加载数据
        async function testLoadData() {
            log('开始测试数据加载...', 'info');
            
            try {
                const result = await DownloadAPI.getDownloadRecords(1, 10);
                
                if (result && result.success && result.records && result.records.length > 0) {
                    log(`✓ 获取到 ${result.records.length} 条记录`, 'success');
                    
                    // 显示记录列表
                    const recordsList = document.getElementById('records-list');
                    if (recordsList) {
                        recordsList.innerHTML = `
                            <div class="download-records-container">
                                <div class="download-group">
                                    <div class="group-header">
                                        <h3 class="group-date">测试数据</h3>
                                        <span class="group-count">${result.records.length} 条记录</span>
                                    </div>
                                    <div class="group-records">
                                        ${result.records.map(record => `
                                            <div class="download-record-card">
                                                <div class="record-main">
                                                    <div class="record-icon">
                                                        <i class="fas fa-file-archive"></i>
                                                    </div>
                                                    <div class="record-info">
                                                        <div class="record-title">
                                                            <h4 class="filename">${record.filename || '未知文件'}</h4>
                                                        </div>
                                                        <div class="record-meta">
                                                            <span class="meta-item">
                                                                <i class="fas fa-hdd"></i>
                                                                ${Utils.formatFileSize(record.file_size || 0)}
                                                            </span>
                                                            <span class="meta-item">
                                                                <i class="fas fa-clock"></i>
                                                                ${new Date(record.download_time || Date.now()).toLocaleString()}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        // 显示记录
                        const states = ['loading-state', 'empty-state'];
                        states.forEach(id => {
                            const el = document.getElementById(id);
                            if (el) el.classList.add('hidden');
                        });
                        recordsList.classList.remove('hidden');
                        
                        log('✓ 记录显示完成', 'success');
                    }
                } else {
                    log('没有找到下载记录，显示空状态', 'warn');
                    const states = ['loading-state', 'records-list'];
                    states.forEach(id => {
                        const el = document.getElementById(id);
                        if (el) el.classList.add('hidden');
                    });
                    const emptyState = document.getElementById('empty-state');
                    if (emptyState) emptyState.classList.remove('hidden');
                }
                
            } catch (error) {
                log(`✗ 数据加载失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成', 'success');
            log('点击"测试API"按钮测试后端接口', 'info');
            log('点击"测试界面"按钮测试前端界面', 'info');
        });
    </script>
</body>
</html>
