/* 下载记录页面样式 */

/* 下载记录包装器 */
.download-records-wrapper {
    padding: 24px;
    background: #f8fafc;
    min-height: calc(100vh - 120px);
}

/* 头部区域 */
.download-records-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
}

.header-left h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0 0 16px 0;
    font-size: 28px;
    font-weight: 700;
    color: #1a202c;
}

.header-left h2 i {
    color: #3182ce;
}

.record-summary {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f7fafc;
    border-radius: 8px;
    font-size: 14px;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.summary-item i {
    color: #3182ce;
}

.download-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 筛选面板 */
.filter-panel {
    margin-bottom: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.filter-panel.hidden {
    display: none;
}

.filter-content {
    padding: 24px;
}

.filter-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 150px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    background: #fff;
    transition: border-color 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.custom-date {
    display: flex;
    align-items: center;
    gap: 8px;
}

.custom-date.hidden {
    display: none;
}

.filter-actions {
    display: flex;
    gap: 12px;
}

/* 主内容区域 */
.download-records-main {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    min-height: 400px;
    position: relative;
}

/* 状态样式 */
.loading-state,
.empty-state,
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
}

.loading-state {
    color: #4a5568;
}

.loading-spinner {
    font-size: 32px;
    margin-bottom: 16px;
    color: #3182ce;
}

.loading-spinner i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.empty-state {
    color: #718096;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.empty-state h3 {
    font-size: 24px;
    margin-bottom: 12px;
    color: #4a5568;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 24px;
    color: #718096;
}

.error-state {
    color: #e53e3e;
}

.error-icon {
    font-size: 64px;
    margin-bottom: 20px;
    color: #feb2b2;
}

.error-state h3 {
    font-size: 24px;
    margin-bottom: 12px;
    color: #e53e3e;
}

.error-state p {
    font-size: 16px;
    margin-bottom: 24px;
    color: #fc8181;
}

/* 记录列表 */
.records-list {
    padding: 24px;
}

.download-records-container {
    max-width: 100%;
}

/* 下载组 */
.download-group {
    margin-bottom: 32px;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e2e8f0;
}

.group-date {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.group-count {
    font-size: 14px;
    color: #718096;
    background: #f7fafc;
    padding: 4px 12px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.group-records {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
}

/* 下载记录卡片 */
.download-record-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.download-record-card:hover {
    border-color: #cbd5e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.download-record-card.error {
    border-color: #feb2b2;
    background: #fef5f5;
}

.record-main {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.record-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: #fff;
    font-size: 20px;
    flex-shrink: 0;
}

.encryption-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 20px;
    height: 20px;
    background: #f6ad55;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #fff;
    border: 2px solid #fff;
}

.record-info {
    flex: 1;
    min-width: 0;
}

.record-title {
    margin-bottom: 8px;
}

.filename {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
    word-break: break-word;
    line-height: 1.4;
}

.record-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.type-badge,
.status-badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge {
    background: #e6fffa;
    color: #234e52;
    border: 1px solid #b2f5ea;
}

.type-single {
    background: #e6fffa;
    color: #234e52;
}

.type-batch {
    background: #fef5e7;
    color: #744210;
}

.type-folder {
    background: #f0f4ff;
    color: #3c366b;
}

.status-badge {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.status-pending {
    background: #fef5e7;
    color: #744210;
}

.status-expired {
    background: #fed7d7;
    color: #c53030;
}

.record-meta {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    color: #718096;
    font-size: 14px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.meta-item i {
    color: #a0aec0;
    font-size: 12px;
}

/* 记录操作 */
.record-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.record-actions .btn {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.record-actions .btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

.record-actions .btn-primary {
    background: #3182ce;
    color: #fff;
    border: 1px solid #3182ce;
}

.record-actions .btn-primary:hover {
    background: #2c5aa0;
    border-color: #2c5aa0;
}

.record-actions .btn-secondary {
    background: #718096;
    color: #fff;
    border: 1px solid #718096;
}

.record-actions .btn-secondary:hover {
    background: #4a5568;
    border-color: #4a5568;
}

.record-actions .btn-outline {
    background: transparent;
    color: #718096;
    border: 1px solid #e2e8f0;
}

.record-actions .btn-outline:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #4a5568;
}

/* 分页控件 */
.pagination-wrapper {
    padding: 24px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 0 0 12px 12px;
}

.pagination-info {
    text-align: center;
    margin-bottom: 16px;
    color: #718096;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.pagination-controls .btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: #fff;
    border: 1px solid #e2e8f0;
    color: #4a5568;
}

.pagination-controls .btn:hover:not(:disabled) {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
}

.pagination-controls .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 4px;
}

.page-number {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: #fff;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    cursor: pointer;
}

.page-number:hover:not(.active):not(:disabled) {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
}

.page-number.active {
    background: #3182ce;
    border-color: #3182ce;
    color: #fff;
}

.page-number:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .download-records-wrapper {
        padding: 16px;
    }

    .download-records-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .record-summary {
        justify-content: center;
    }

    .download-controls {
        justify-content: center;
    }

    .filter-row {
        flex-direction: column;
        gap: 16px;
    }

    .filter-group {
        min-width: auto;
    }

    .group-records {
        grid-template-columns: 1fr;
    }

    .record-main {
        flex-direction: column;
        text-align: center;
    }

    .record-actions {
        justify-content: center;
    }

    .pagination-controls {
        flex-wrap: wrap;
        gap: 4px;
    }

    .pagination-controls .btn {
        min-width: 36px;
        height: 36px;
        font-size: 13px;
    }

    .page-number {
        min-width: 36px;
        height: 36px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .download-records-wrapper {
        padding: 12px;
    }

    .download-records-header {
        padding: 16px;
    }

    .header-left h2 {
        font-size: 24px;
    }

    .record-summary {
        flex-direction: column;
        gap: 12px;
    }

    .summary-item {
        justify-content: center;
    }

    .filter-content {
        padding: 16px;
    }

    .records-list {
        padding: 16px;
    }

    .download-record-card {
        padding: 16px;
    }

    .record-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .filename {
        font-size: 15px;
    }

    .record-meta {
        flex-direction: column;
        gap: 8px;
    }

    .pagination-wrapper {
        padding: 16px;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 按钮基础样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: inherit;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #3182ce;
    color: #fff;
    border-color: #3182ce;
}

.btn-primary:hover:not(:disabled) {
    background: #2c5aa0;
    border-color: #2c5aa0;
}

.btn-secondary {
    background: #718096;
    color: #fff;
    border-color: #718096;
}

.btn-secondary:hover:not(:disabled) {
    background: #4a5568;
    border-color: #4a5568;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

/* 下载记录视图 */
.download-records-view {
    padding: 24px;
    background: #f9fafb;
    min-height: calc(100vh - 120px);
}

.download-records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.download-records-header h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
}

.download-records-header h2 i {
    color: #2563eb;
}

.download-records-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.download-records-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 400px;
}

.download-records-list {
    padding: 24px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

/* 下载记录标签页 */
.download-records-tabs {
    width: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-header {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.tab-btn {
    flex: 1;
    padding: 16px 24px;
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.tab-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.tab-btn.active {
    background: #fff;
    color: #2563eb;
    border-bottom: 2px solid #2563eb;
}

.tab-btn i {
    margin-right: 8px;
}

.tab-content {
    min-height: 400px;
}

.tab-panel {
    display: none;
    padding: 24px;
}

.tab-panel.active {
    display: block;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #374151;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.6;
}

/* 加载状态 */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    color: #6b7280;
}

.loading-state i {
    font-size: 32px;
    color: #2563eb;
    margin-bottom: 16px;
}

.loading-state p {
    font-size: 14px;
}

/* 错误状态 */
.error-state {
    text-align: center;
    padding: 80px 20px;
    color: #dc2626;
}

.error-state i {
    font-size: 48px;
    color: #fca5a5;
    margin-bottom: 16px;
}

.error-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
}

.error-state p {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 24px;
}

/* 下载记录容器 */
.download-records-container {
    max-width: 100%;
}

.download-group {
    margin-bottom: 32px;
}

.download-date {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.download-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 下载记录卡片 */
.download-record-card {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
    position: relative;
}

.download-record-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.record-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.record-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: #eff6ff;
    border-radius: 8px;
    margin-right: 16px;
    position: relative;
}

.record-icon i {
    font-size: 20px;
    color: #2563eb;
}

.encrypted-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 16px;
    height: 16px;
    background: #f59e0b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: #fff;
    border: 2px solid #fff;
}

.record-info {
    flex: 1;
}

.record-filename {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
    line-height: 1.4;
    word-break: break-word;
}

.record-meta {
    display: flex;
    gap: 16px;
    font-size: 13px;
    color: #6b7280;
}

.record-meta span {
    display: flex;
    align-items: center;
}

/* 记录统计 */
.record-stats {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 24px;
}

.download-progress {
    flex: 1;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #6b7280;
}

.encryption-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #f59e0b;
    background: #fef3c7;
    padding: 4px 8px;
    border-radius: 4px;
}

.encryption-status i {
    font-size: 10px;
}

/* 记录操作 */
.record-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn {
    padding: 8px 16px;
    border: 1px solid;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-primary {
    background: #2563eb;
    border-color: #2563eb;
    color: #fff;
}

.btn-primary:hover {
    background: #1d4ed8;
    border-color: #1d4ed8;
}

.btn-secondary {
    background: #6b7280;
    border-color: #6b7280;
    color: #fff;
}

.btn-secondary:hover {
    background: #4b5563;
    border-color: #4b5563;
}

.btn-outline {
    background: transparent;
    border-color: #d1d5db;
    color: #6b7280;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.btn i {
    font-size: 11px;
}

/* 状态徽章 */
.expired-badge,
.pending-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.expired-badge {
    color: #dc2626;
    background: #fee2e2;
}

.pending-badge {
    color: #f59e0b;
    background: #fef3c7;
}

/* 密码申请记录 */
.password-requests-container {
    max-width: 100%;
}

.request-group {
    margin-bottom: 32px;
}

.request-status-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.request-status-header.pending {
    color: #f59e0b;
}

.request-status-header.approved {
    color: #10b981;
}

.request-status-header.rejected {
    color: #dc2626;
}

.request-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 密码申请卡片 */
.password-request-card {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.password-request-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.password-request-card.pending {
    border-left: 4px solid #f59e0b;
}

.password-request-card.approved {
    border-left: 4px solid #10b981;
}

.password-request-card.rejected {
    border-left: 4px solid #dc2626;
}

.request-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.request-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #f3f4f6;
    border-radius: 6px;
    margin-right: 12px;
}

.request-icon i {
    font-size: 16px;
    color: #6b7280;
}

.request-info {
    flex: 1;
}

.request-filename {
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.request-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 12px;
    color: #6b7280;
}

.request-content {
    margin-bottom: 16px;
}

.request-reason,
.approval-reason {
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 8px;
}

.request-reason {
    color: #374151;
}

.approval-reason {
    color: #059669;
}

.request-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 对话框样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.password-request-dialog,
.password-result-dialog,
.password-display-dialog {
    background: #fff;
    border-radius: 8px;
    width: 100%;
    max-width: 480px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0 24px;
    margin-bottom: 20px;
}

.dialog-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.dialog-content {
    padding: 0 24px 20px 24px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 6px;
    margin-bottom: 20px;
}

.file-info i {
    font-size: 20px;
    color: #2563eb;
}

.file-name {
    font-weight: 500;
    color: #1f2937;
}

.info-text {
    margin-bottom: 16px;
}

.info-text p {
    margin: 0 0 8px 0;
    color: #374151;
    line-height: 1.5;
}

.reason-input {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    margin-bottom: 16px;
}

.reason-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.remaining-requests {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 20px;
}

.remaining-count {
    font-weight: 500;
}

.dialog-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px 24px 24px;
    border-top: 1px solid #e5e7eb;
}

/* 密码显示 */
.password-display {
    margin-bottom: 20px;
}

.password-display label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.password-box,
.password-field {
    display: flex;
    gap: 8px;
}

.password-input {
    flex: 1;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    background: #f9fafb;
}

.password-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.copy-btn {
    padding: 12px;
    background: #2563eb;
    border: 1px solid #2563eb;
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background: #1d4ed8;
    border-color: #1d4ed8;
}

.copy-btn.copied {
    background: #10b981;
    border-color: #10b981;
}

.password-info,
.password-note {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.5;
}

.password-info p,
.password-note {
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.password-info i,
.password-note i {
    color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .download-records-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) {
    .download-records-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .download-records-view {
        padding: 16px;
    }
    
    .download-records-list {
        grid-template-columns: 1fr;
        padding: 16px;
    }
    
    .dialog-overlay {
        padding: 10px;
    }
    
    .tab-header {
        flex-direction: column;
    }
    
    .tab-btn {
        padding: 12px 16px;
    }
    
    .tab-panel {
        padding: 16px;
    }
    
    .download-record-card,
    .password-request-card {
        padding: 16px;
    }
    
    .record-header,
    .request-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .record-icon,
    .request-icon {
        margin-right: 0;
    }
    
    .record-meta {
        flex-direction: column;
        gap: 4px;
    }
    
    .record-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .record-actions,
    .request-actions {
        flex-wrap: wrap;
    }
    
    .dialog-header,
    .dialog-content,
    .dialog-actions {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .download-records-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
        padding: 16px;
    }
    
    .download-records-controls {
        justify-content: center;
    }
} 