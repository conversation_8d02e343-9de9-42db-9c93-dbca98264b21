#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载记录管理窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import threading

class DownloadRecordsWindow:
    """下载记录管理窗口类"""
    
    def __init__(self, parent, server_instance):
        self.parent = parent
        self.server = server_instance
        self.window = None
        self.records_tree = None
        self.current_records = []
        self.current_page = 1
        self.page_size = 50
        self.total_records = 0
        self.filter_user_id = None
        self.filter_date_from = None
        self.filter_date_to = None
        
    def show(self):
        """显示下载记录管理窗口"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("下载记录管理")
        self.window.geometry("1400x800")
        self.window.transient(self.parent)
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.create_widgets()
        self.load_users()  # 加载用户列表
        self.load_records()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 工具栏
        self.create_toolbar(main_frame)
        
        # 筛选框架
        self.create_filter_frame(main_frame)
        
        # 记录列表
        self.create_records_list(main_frame)
        
        # 分页控制
        self.create_pagination(main_frame)
        
        # 详情框架
        self.create_details_frame(main_frame)
        
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar_frame, text="刷新", command=self.refresh_records).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="导出记录", command=self.export_records).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="清理旧记录", command=self.cleanup_old_records).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="统计分析", command=self.show_statistics).pack(side=tk.LEFT, padx=(0, 5))
        
        # 右侧信息
        info_frame = ttk.Frame(toolbar_frame)
        info_frame.pack(side=tk.RIGHT)
        
        self.total_label = ttk.Label(info_frame, text="总记录数: 0")
        self.total_label.pack(side=tk.RIGHT, padx=(10, 0))
        
    def create_filter_frame(self, parent):
        """创建筛选框架"""
        filter_frame = ttk.LabelFrame(parent, text="筛选条件")
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行筛选条件
        row1 = ttk.Frame(filter_frame)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # 用户筛选
        ttk.Label(row1, text="用户:").pack(side=tk.LEFT, padx=(0, 5))
        self.user_var = tk.StringVar()
        user_combo = ttk.Combobox(row1, textvariable=self.user_var, width=15, state="readonly")
        user_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.user_combo = user_combo
        
        # 下载类型筛选
        ttk.Label(row1, text="类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.type_var = tk.StringVar()
        type_combo = ttk.Combobox(row1, textvariable=self.type_var, width=12, state="readonly")
        type_combo['values'] = ('全部', 'single', 'batch', 'folder')
        type_combo.set('全部')
        type_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 加密状态筛选
        ttk.Label(row1, text="加密:").pack(side=tk.LEFT, padx=(0, 5))
        self.encrypted_var = tk.StringVar()
        encrypted_combo = ttk.Combobox(row1, textvariable=self.encrypted_var, width=10, state="readonly")
        encrypted_combo['values'] = ('全部', '是', '否')
        encrypted_combo.set('全部')
        encrypted_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 第二行筛选条件
        row2 = ttk.Frame(filter_frame)
        row2.pack(fill=tk.X, padx=10, pady=5)
        
        # 日期范围
        ttk.Label(row2, text="日期范围:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.date_from_var = tk.StringVar()
        date_from_entry = ttk.Entry(row2, textvariable=self.date_from_var, width=12)
        date_from_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(row2, text="至").pack(side=tk.LEFT, padx=(0, 5))
        
        self.date_to_var = tk.StringVar()
        date_to_entry = ttk.Entry(row2, textvariable=self.date_to_var, width=12)
        date_to_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # 快速日期选择
        ttk.Button(row2, text="今天", command=lambda: self.set_date_range(0)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(row2, text="昨天", command=lambda: self.set_date_range(1)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(row2, text="本周", command=lambda: self.set_date_range(7)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(row2, text="本月", command=lambda: self.set_date_range(30)).pack(side=tk.LEFT, padx=(0, 10))
        
        # 搜索按钮
        ttk.Button(row2, text="应用筛选", command=self.apply_filters).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(row2, text="清除筛选", command=self.clear_filters).pack(side=tk.LEFT)
        
    def create_records_list(self, parent):
        """创建记录列表"""
        list_frame = ttk.LabelFrame(parent, text="下载记录")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建树形视图
        columns = ("ID", "用户", "文件名", "大小", "类型", "加密", "下载时间", "IP地址", "状态")
        self.records_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {
            "ID": 60,
            "用户": 100,
            "文件名": 300,
            "大小": 80,
            "类型": 80,
            "加密": 60,
            "下载时间": 150,
            "IP地址": 120,
            "状态": 80
        }
        
        for col in columns:
            self.records_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.records_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.records_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.records_tree.xview)
        self.records_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.records_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定选择事件
        self.records_tree.bind('<<TreeviewSelect>>', self.on_record_select)
        self.records_tree.bind('<Double-1>', self.on_record_double_click)
        
    def create_pagination(self, parent):
        """创建分页控制"""
        page_frame = ttk.Frame(parent)
        page_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 页面信息
        self.page_info_label = ttk.Label(page_frame, text="第 1 页，共 1 页")
        self.page_info_label.pack(side=tk.LEFT)
        
        # 分页按钮
        button_frame = ttk.Frame(page_frame)
        button_frame.pack(side=tk.RIGHT)
        
        self.first_btn = ttk.Button(button_frame, text="首页", command=self.go_first_page, state=tk.DISABLED)
        self.first_btn.pack(side=tk.LEFT, padx=2)
        
        self.prev_btn = ttk.Button(button_frame, text="上一页", command=self.go_prev_page, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=2)
        
        self.next_btn = ttk.Button(button_frame, text="下一页", command=self.go_next_page, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=2)
        
        self.last_btn = ttk.Button(button_frame, text="末页", command=self.go_last_page, state=tk.DISABLED)
        self.last_btn.pack(side=tk.LEFT, padx=2)
        
        # 页面大小选择
        ttk.Label(button_frame, text="每页:").pack(side=tk.LEFT, padx=(10, 2))
        self.page_size_var = tk.StringVar(value=str(self.page_size))
        page_size_combo = ttk.Combobox(button_frame, textvariable=self.page_size_var, width=8, state="readonly")
        page_size_combo['values'] = ('25', '50', '100', '200')
        page_size_combo.bind('<<ComboboxSelected>>', self.on_page_size_change)
        page_size_combo.pack(side=tk.LEFT, padx=2)
        
    def create_details_frame(self, parent):
        """创建详情框架"""
        details_frame = ttk.LabelFrame(parent, text="记录详情")
        details_frame.pack(fill=tk.X)
        
        # 创建文本框显示详情
        self.details_text = tk.Text(details_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        details_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        self.details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        details_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)

    def load_records(self):
        """加载下载记录"""
        try:
            # 获取下载服务
            download_service = self.server.services.get('download')
            if not download_service:
                messagebox.showerror("错误", "下载服务不可用")
                return

            # 构建筛选条件
            filters = {}
            if self.filter_user_id:
                filters['user_id'] = self.filter_user_id
            if self.filter_date_from:
                filters['date_from'] = self.filter_date_from
            if self.filter_date_to:
                filters['date_to'] = self.filter_date_to

            # 获取记录
            result = download_service.get_all_download_records(
                page=self.current_page,
                limit=self.page_size,
                filters=filters
            )

            if result.get('success', False):
                self.current_records = result.get('records', [])
                self.total_records = result.get('total', 0)
                self.update_records_display()
                self.update_pagination_controls()
                self.update_total_label()
            else:
                messagebox.showerror("错误", f"加载记录失败: {result.get('error', '未知错误')}")

        except Exception as e:
            messagebox.showerror("错误", f"加载记录失败: {e}")

    def update_records_display(self):
        """更新记录显示"""
        # 清空现有记录
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)

        # 添加新记录
        for record in self.current_records:
            # 格式化数据
            user_name = record.get('username', '未知用户')
            filename = record.get('filename', '未知文件')
            if len(filename) > 40:
                filename = filename[:37] + "..."

            file_size = self.format_file_size(record.get('file_size', 0))
            download_type = record.get('download_type', 'unknown')
            is_encrypted = "是" if record.get('is_encrypted', False) else "否"
            download_time = self.format_datetime(record.get('download_time'))
            ip_address = record.get('ip_address', '未知')
            status = record.get('download_status', 'unknown')

            # 插入记录
            self.records_tree.insert('', 'end', values=(
                record.get('id', ''),
                user_name,
                filename,
                file_size,
                download_type,
                is_encrypted,
                download_time,
                ip_address,
                status
            ))

    def update_pagination_controls(self):
        """更新分页控制"""
        total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)

        # 更新页面信息
        self.page_info_label.config(text=f"第 {self.current_page} 页，共 {total_pages} 页")

        # 更新按钮状态
        self.first_btn.config(state=tk.NORMAL if self.current_page > 1 else tk.DISABLED)
        self.prev_btn.config(state=tk.NORMAL if self.current_page > 1 else tk.DISABLED)
        self.next_btn.config(state=tk.NORMAL if self.current_page < total_pages else tk.DISABLED)
        self.last_btn.config(state=tk.NORMAL if self.current_page < total_pages else tk.DISABLED)

    def update_total_label(self):
        """更新总记录数标签"""
        self.total_label.config(text=f"总记录数: {self.total_records}")

    def load_users(self):
        """加载用户列表用于筛选"""
        try:
            user_service = self.server.services.get('user')
            if user_service:
                users = user_service.get_all_users()
                if users.get('success', False):
                    user_list = ['全部'] + [f"{user['username']} ({user['id']})" for user in users.get('users', [])]
                    self.user_combo['values'] = user_list
                    self.user_combo.set('全部')
        except Exception as e:
            print(f"加载用户列表失败: {e}")

    def format_file_size(self, size):
        """格式化文件大小"""
        if not size:
            return "0 B"

        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return "未知时间"

        try:
            if isinstance(dt_str, str):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            else:
                dt = dt_str
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(dt_str)

    def set_date_range(self, days_ago):
        """设置日期范围"""
        if days_ago == 0:  # 今天
            today = datetime.now().date()
            self.date_from_var.set(today.strftime("%Y-%m-%d"))
            self.date_to_var.set(today.strftime("%Y-%m-%d"))
        elif days_ago == 1:  # 昨天
            yesterday = (datetime.now() - timedelta(days=1)).date()
            self.date_from_var.set(yesterday.strftime("%Y-%m-%d"))
            self.date_to_var.set(yesterday.strftime("%Y-%m-%d"))
        else:  # 指定天数前到今天
            start_date = (datetime.now() - timedelta(days=days_ago)).date()
            end_date = datetime.now().date()
            self.date_from_var.set(start_date.strftime("%Y-%m-%d"))
            self.date_to_var.set(end_date.strftime("%Y-%m-%d"))

    def apply_filters(self):
        """应用筛选条件"""
        # 解析用户筛选
        user_text = self.user_var.get()
        if user_text and user_text != '全部':
            try:
                # 提取用户ID
                self.filter_user_id = int(user_text.split('(')[-1].split(')')[0])
            except:
                self.filter_user_id = None
        else:
            self.filter_user_id = None

        # 解析日期筛选
        self.filter_date_from = self.date_from_var.get() if self.date_from_var.get() else None
        self.filter_date_to = self.date_to_var.get() if self.date_to_var.get() else None

        # 重置到第一页并加载
        self.current_page = 1
        self.load_records()

    def clear_filters(self):
        """清除筛选条件"""
        self.user_var.set('全部')
        self.type_var.set('全部')
        self.encrypted_var.set('全部')
        self.date_from_var.set('')
        self.date_to_var.set('')
        self.filter_user_id = None
        self.filter_date_from = None
        self.filter_date_to = None
        self.current_page = 1
        self.load_records()

    # 分页控制方法
    def go_first_page(self):
        """跳转到首页"""
        self.current_page = 1
        self.load_records()

    def go_prev_page(self):
        """跳转到上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_records()

    def go_next_page(self):
        """跳转到下一页"""
        total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        if self.current_page < total_pages:
            self.current_page += 1
            self.load_records()

    def go_last_page(self):
        """跳转到末页"""
        total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        self.current_page = total_pages
        self.load_records()

    def on_page_size_change(self, event):
        """页面大小改变事件"""
        self.page_size = int(self.page_size_var.get())
        self.current_page = 1
        self.load_records()

    # 事件处理方法
    def on_record_select(self, event):
        """记录选择事件"""
        selection = self.records_tree.selection()
        if selection:
            item = selection[0]
            record_id = self.records_tree.item(item)['values'][0]
            self.show_record_details(record_id)

    def on_record_double_click(self, event):
        """记录双击事件"""
        selection = self.records_tree.selection()
        if selection:
            item = selection[0]
            record_id = self.records_tree.item(item)['values'][0]
            self.show_record_detail_dialog(record_id)

    def show_record_details(self, record_id):
        """在详情框中显示记录详情"""
        try:
            # 查找对应的记录
            record = None
            for r in self.current_records:
                if r.get('id') == record_id:
                    record = r
                    break

            if not record:
                return

            # 格式化详情信息
            details = []
            details.append(f"记录ID: {record.get('id', 'N/A')}")
            details.append(f"用户: {record.get('username', 'N/A')} (ID: {record.get('user_id', 'N/A')})")
            details.append(f"文件名: {record.get('filename', 'N/A')}")
            details.append(f"文件ID: {record.get('file_id', 'N/A')}")
            details.append(f"文件大小: {self.format_file_size(record.get('file_size', 0))}")
            details.append(f"下载类型: {record.get('download_type', 'N/A')}")
            details.append(f"是否加密: {'是' if record.get('is_encrypted', False) else '否'}")
            details.append(f"下载时间: {self.format_datetime(record.get('download_time'))}")
            details.append(f"IP地址: {record.get('ip_address', 'N/A')}")
            details.append(f"用户代理: {record.get('user_agent', 'N/A')}")
            details.append(f"下载来源: {record.get('download_source', 'N/A')}")
            details.append(f"状态: {record.get('download_status', 'N/A')}")
            details.append(f"ZIP路径: {record.get('zip_path', 'N/A')}")

            # 显示详情
            self.details_text.config(state=tk.NORMAL)
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(1.0, '\n'.join(details))
            self.details_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"显示记录详情失败: {e}")

    def show_record_detail_dialog(self, record_id):
        """显示记录详情对话框"""
        try:
            # 查找对应的记录
            record = None
            for r in self.current_records:
                if r.get('id') == record_id:
                    record = r
                    break

            if not record:
                return

            # 创建详情对话框
            dialog = tk.Toplevel(self.window)
            dialog.title(f"下载记录详情 - ID: {record_id}")
            dialog.geometry("600x500")
            dialog.transient(self.window)
            dialog.grab_set()

            # 创建详情文本框
            text_frame = ttk.Frame(dialog)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, state=tk.DISABLED)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 填充详情信息
            details = []
            for key, value in record.items():
                if key == 'file_size':
                    value = self.format_file_size(value)
                elif key in ['download_time', 'created_at']:
                    value = self.format_datetime(value)
                elif key == 'is_encrypted':
                    value = '是' if value else '否'

                details.append(f"{key}: {value}")

            text_widget.config(state=tk.NORMAL)
            text_widget.insert(1.0, '\n'.join(details))
            text_widget.config(state=tk.DISABLED)

            # 关闭按钮
            ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

        except Exception as e:
            messagebox.showerror("错误", f"显示详情失败: {e}")

    def sort_by_column(self, column):
        """按列排序"""
        # TODO: 实现排序功能
        pass

    def refresh_records(self):
        """刷新记录"""
        self.load_records()
        messagebox.showinfo("成功", "记录已刷新")

    def export_records(self):
        """导出记录"""
        messagebox.showinfo("导出记录", "记录导出功能正在开发中...")

    def cleanup_old_records(self):
        """清理旧记录"""
        result = messagebox.askyesno("确认", "确定要清理30天前的下载记录吗？\n此操作不可撤销！")
        if result:
            try:
                download_service = self.server.services.get('download')
                if download_service:
                    # TODO: 实现清理功能
                    messagebox.showinfo("成功", "旧记录清理功能正在开发中...")
                else:
                    messagebox.showerror("错误", "下载服务不可用")
            except Exception as e:
                messagebox.showerror("错误", f"清理失败: {e}")

    def show_statistics(self):
        """显示统计分析"""
        messagebox.showinfo("统计分析", "统计分析功能正在开发中...")

    def on_closing(self):
        """窗口关闭事件"""
        self.window.destroy()
        self.window = None
