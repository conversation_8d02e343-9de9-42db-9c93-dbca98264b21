2025-06-10 20:35:44 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 21:40:21 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 21:40:21 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 21:43:51 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 21:43:51 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 21:44:05 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 21:44:05 - DownloadService - ERROR - 创建加密压缩文件失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:48 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:48 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:55 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:55 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:56 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:56 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:57 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-10 22:28:57 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 19:48:02 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试2\3.jpg for reading
2025-06-11 19:51:25 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试2\3.jpg for reading
2025-06-11 19:51:28 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试2\3.jpg for reading
2025-06-11 19:52:09 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 19:52:09 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 19:52:29 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 19:52:29 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 19:54:09 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 19:54:09 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:00:37 - DownloadService - ERROR - 获取用户下载记录失败: (pymysql.err.OperationalError) (1054, "Unknown column 'download_records.folder_id' in 'field list'")
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records 
WHERE download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC 
 LIMIT %(param_1)s, %(param_2)s]
[parameters: {'param_1': 0, 'param_2': 50}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:00:38 - DownloadService - ERROR - 获取用户下载记录失败: (pymysql.err.OperationalError) (1054, "Unknown column 'download_records.folder_id' in 'field list'")
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records 
WHERE download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC 
 LIMIT %(param_1)s, %(param_2)s]
[parameters: {'param_1': 0, 'param_2': 50}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:01:58 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:01:58 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200158.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200158.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'bKQq5&8uHEL@', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 1, 58, 583491)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:01:58 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:01:58 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200158.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200158.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'GtZ5Bs&LwjMk', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 1, 58, 683316)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:07:24 - DownloadService - ERROR - 创建普通压缩文件失败: [Errno 13] Permission denied: 'D:\\HLDB\\AIS2018101755337.mdf'
2025-06-11 20:07:24 - DownloadService - ERROR - 创建普通压缩文件失败: [Errno 13] Permission denied: 'D:\\HLDB\\AIS2018101755337.mdf'
2025-06-11 20:07:29 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:07:29 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200729.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200729.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'T7uE@i5D004P', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 7, 29, 970176)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:07:30 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:07:30 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200730.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200730.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'dY3V*6N@y9GS', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 7, 30, 58939)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:07:43 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:07:43 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200743.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200743.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'k3e$%8OVk$Gp', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 7, 43, 550913)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:07:43 - DownloadService - ERROR - pyminizip压缩失败: error in closing C:\Users\<USER>\Desktop\Net\backend\temp\downloads\folder_测试_20250611_200743.zip (-102)
2025-06-11 20:07:43 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200743.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200743.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'rpzxqiW$Y1M4', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 7, 43, 642667)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:07:45 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:07:45 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200745.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200745.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': '%1xeIeGYb6Hi', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 7, 45, 840182)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:07:45 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:07:45 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200745.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200745.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': '2!C$hcg^67Cz', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 7, 45, 922961)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:08:56 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:08:56 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200856.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200856.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'YRR6L7@GyV07', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 8, 56, 921503)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:09:03 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:09:03 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200903.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200903.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'c59%Vye0ak@G', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 9, 3, 197998)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:09:05 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:09:05 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200905.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200905.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': '6127lz1N^GB5', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 9, 5, 713822)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:09:07 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:09:07 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_200907.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_200907.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'fnqJ9K1@xI^E', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 9, 7, 114514)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:09:09 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试2\3.jpg for reading
2025-06-11 20:09:09 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 4, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试2_20250611_200909.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试2_20250611_200909.zip', 'file_size': 18182448, 'is_encrypted': 1, 'password': 's2w3XhrBiYT&', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 9, 9, 779165)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:20:20 - DownloadService - ERROR - 获取用户下载记录失败: (pymysql.err.OperationalError) (1054, "Unknown column 'download_records.folder_id' in 'field list'")
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records 
WHERE download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC 
 LIMIT %(param_1)s, %(param_2)s]
[parameters: {'param_1': 0, 'param_2': 50}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:20:28 - DownloadService - ERROR - 获取用户下载记录失败: (pymysql.err.OperationalError) (1054, "Unknown column 'download_records.folder_id' in 'field list'")
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records 
WHERE download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC 
 LIMIT %(param_1)s, %(param_2)s]
[parameters: {'param_1': 0, 'param_2': 50}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:21:12 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-11 20:21:12 - DownloadService - ERROR - 记录下载失败: (pymysql.err.OperationalError) (1054, "Unknown column 'folder_id' in 'field list'")
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (%(file_id)s, %(folder_id)s, %(user_id)s, %(download_type)s, %(zip_filename)s, %(zip_path)s, %(file_size)s, %(is_encrypted)s, %(password)s, %(password_hint)s, %(download_status)s, %(expires_at)s, now(), %(downloaded_at)s)]
[parameters: {'file_id': None, 'folder_id': 2, 'user_id': None, 'download_type': 'folder', 'zip_filename': 'folder_测试_20250611_202112.zip', 'zip_path': 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_测试_20250611_202112.zip', 'file_size': 44917, 'is_encrypted': 1, 'password': 'lYz9#XXbux$5', 'password_hint': None, 'download_status': 'completed', 'expires_at': None, 'downloaded_at': datetime.datetime(2025, 6, 11, 20, 21, 12, 211366)}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-11 20:33:28 - DownloadService - ERROR - pyminizip压缩失败: error in opening D:\测试\5efedafd65dce35beead5d6ad884233a.jpeg for reading
2025-06-12 00:40:41 - DownloadService - ERROR - 获取用户下载记录失败: Attribute name 'metadata' is reserved when using the Declarative API.
2025-06-12 00:40:41 - DownloadService - ERROR - 获取用户下载记录失败: Attribute name 'metadata' is reserved when using the Declarative API.
2025-06-12 00:47:45 - DownloadService - ERROR - 更新用户活动统计失败: unsupported operand type(s) for +=: 'NoneType' and 'int'
2025-06-12 09:37:01 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such table: download_batches
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:37:01 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such table: download_batches
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:46:40 - DownloadService - ERROR - 更新用户活动统计失败: unsupported operand type(s) for +=: 'NoneType' and 'int'
2025-06-12 09:46:40 - DownloadService - ERROR - 记录下载失败: (sqlite3.OperationalError) table download_records has no column named batch_id
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, batch_id, session_id, ip_address, user_agent, download_source, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?) RETURNING id, created_at]
[parameters: (None, 4, None, 1, None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.2.384', 'web', 'folder', 'folder_设计图纸_20250612_094640.zip', 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_设计图纸_20250612_094640.zip', 2161690, 0, None, None, 'completed', None, '2025-06-12 09:46:40.535743')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:47:34 - DownloadService - ERROR - 更新用户活动统计失败: unsupported operand type(s) for +=: 'NoneType' and 'int'
2025-06-12 09:47:34 - DownloadService - ERROR - 记录下载失败: (sqlite3.OperationalError) table download_records has no column named batch_id
[SQL: INSERT INTO download_records (file_id, folder_id, user_id, batch_id, session_id, ip_address, user_agent, download_source, download_type, zip_filename, zip_path, file_size, is_encrypted, password, password_hint, download_status, expires_at, created_at, downloaded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?) RETURNING id, created_at]
[parameters: (None, 4, None, 2, None, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.1.2.384', 'web', 'folder', 'folder_设计图纸_20250612_094734.zip', 'C:\\Users\\<USER>\\Desktop\\Net\\backend\\temp\\downloads\\folder_设计图纸_20250612_094734.zip', 2161690, 0, None, None, 'completed', None, '2025-06-12 09:47:34.197007')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:47:37 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:47:37 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:49:20 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:49:20 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:55:34 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:55:34 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 09:56:05 - DownloadService - ERROR - 获取用户下载记录失败: (sqlite3.OperationalError) no such column: download_records.batch_id
[SQL: SELECT download_records.id AS download_records_id, download_records.file_id AS download_records_file_id, download_records.folder_id AS download_records_folder_id, download_records.user_id AS download_records_user_id, download_records.batch_id AS download_records_batch_id, download_records.session_id AS download_records_session_id, download_records.ip_address AS download_records_ip_address, download_records.user_agent AS download_records_user_agent, download_records.download_source AS download_records_download_source, download_records.download_type AS download_records_download_type, download_records.zip_filename AS download_records_zip_filename, download_records.zip_path AS download_records_zip_path, download_records.file_size AS download_records_file_size, download_records.is_encrypted AS download_records_is_encrypted, download_records.password AS download_records_password, download_records.password_hint AS download_records_password_hint, download_records.download_status AS download_records_download_status, download_records.expires_at AS download_records_expires_at, download_records.created_at AS download_records_created_at, download_records.downloaded_at AS download_records_downloaded_at 
FROM download_records LEFT OUTER JOIN download_batches ON download_records.batch_id = download_batches.id 
WHERE download_records.user_id = ? OR download_records.user_id IS NULL ORDER BY download_records.downloaded_at DESC
 LIMIT ? OFFSET ?]
[parameters: (2, 50, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-12 14:35:20 - DownloadService - ERROR - 获取所有下载记录失败: 'NoneType' object is not callable
2025-06-12 14:35:59 - DownloadService - ERROR - 获取所有下载记录失败: 'NoneType' object is not callable
