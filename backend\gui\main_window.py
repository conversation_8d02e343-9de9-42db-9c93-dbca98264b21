#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
from datetime import datetime
from typing import Optional

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: tk.Tk, server_instance):
        self.root = root
        self.server = server_instance
        self.setup_window()
        self.create_widgets()
        self.setup_layout()
        self.start_status_update()

        # 自动启动服务器（延迟2秒）
        self.root.after(2000, self.auto_start_server)
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("企业级文件共享系统 - 服务端 v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_statusbar()
        
        # 创建主要内容区域
        self.create_main_content()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 服务器菜单
        server_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="服务器", menu=server_menu)
        server_menu.add_command(label="启动服务器", command=self.start_server)
        server_menu.add_command(label="停止服务器", command=self.stop_server)
        server_menu.add_command(label="重启服务器", command=self.restart_server)
        server_menu.add_separator()
        server_menu.add_command(label="服务器设置", command=self.open_server_settings)
        
        # 管理菜单
        manage_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="管理", menu=manage_menu)
        manage_menu.add_command(label="用户管理", command=self.open_user_management)
        manage_menu.add_command(label="文件管理", command=self.open_file_management)
        manage_menu.add_command(label="下载记录", command=self.open_download_records)
        manage_menu.add_command(label="权限管理", command=self.open_permission_management)
        manage_menu.add_command(label="活动日志", command=self.open_activity_log)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据库备份", command=self.backup_database)
        tools_menu.add_command(label="数据库恢复", command=self.restore_database)
        tools_menu.add_command(label="清理缓存", command=self.clear_cache)
        tools_menu.add_command(label="系统诊断", command=self.system_diagnosis)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用手册", command=self.show_manual)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.main_frame)
        
        # 服务器控制按钮
        self.start_btn = ttk.Button(self.toolbar, text="启动服务器", 
                                   command=self.start_server)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(self.toolbar, text="停止服务器", 
                                  command=self.stop_server, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                            fill=tk.Y, padx=5)
        
        # 管理按钮
        ttk.Button(self.toolbar, text="用户管理",
                  command=self.open_user_management).pack(side=tk.LEFT, padx=2)

        ttk.Button(self.toolbar, text="文件管理",
                  command=self.open_file_management).pack(side=tk.LEFT, padx=2)

        ttk.Button(self.toolbar, text="下载记录",
                  command=self.open_download_records).pack(side=tk.LEFT, padx=2)

        ttk.Button(self.toolbar, text="监控",
                  command=self.open_monitoring).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                            fill=tk.Y, padx=5)
        
        # 设置按钮
        ttk.Button(self.toolbar, text="设置", 
                  command=self.open_settings).pack(side=tk.LEFT, padx=2)
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = ttk.Frame(self.main_frame)
        
        # 服务器状态
        self.status_label = ttk.Label(self.statusbar, text="服务器已停止")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                              fill=tk.Y, padx=5)
        
        # 端口信息
        self.port_label = ttk.Label(self.statusbar, text="端口: --")
        self.port_label.pack(side=tk.LEFT, padx=5)

        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT,
                                                              fill=tk.Y, padx=5)

        # 在线用户数
        self.users_label = ttk.Label(self.statusbar, text="在线用户: 0")
        self.users_label.pack(side=tk.LEFT, padx=5)

        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT,
                                                              fill=tk.Y, padx=5)

        # 系统时间
        self.time_label = ttk.Label(self.statusbar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)

        # 更新端口信息
        self.update_port_info()
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 概览标签页
        self.create_overview_tab()
        
        # 实时监控标签页
        self.create_monitoring_tab()
        
        # 系统日志标签页
        self.create_log_tab()
        
        # 通知标签页
        self.create_notification_tab()
        
        # 滚动字幕标签页
        self.create_marquee_tab()
    
    def create_overview_tab(self):
        """创建概览标签页"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="系统概览")
        
        # 左侧统计信息
        left_frame = ttk.LabelFrame(overview_frame, text="系统统计")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 统计信息标签
        self.stats_labels = {}
        stats_items = [
            ("总用户数", "total_users"),
            ("在线用户", "online_users"),
            ("共享文件夹", "shared_folders"),
            ("总文件数", "total_files"),
            ("总文件大小", "total_size"),
            ("今日下载", "today_downloads"),
            ("今日上传", "today_uploads"),
            ("今日搜索", "today_searches")
        ]
        
        for i, (label, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            ttk.Label(left_frame, text=f"{label}:").grid(row=row, column=col*2, 
                                                        sticky=tk.W, padx=5, pady=2)
            self.stats_labels[key] = ttk.Label(left_frame, text="0")
            self.stats_labels[key].grid(row=row, column=col*2+1, sticky=tk.W, 
                                       padx=5, pady=2)
        
        # 右侧快速操作
        right_frame = ttk.LabelFrame(overview_frame, text="快速操作")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        quick_actions = [
            ("添加共享文件夹", self.add_shared_folder),
            ("创建用户", self.create_user),
            ("查看活动日志", self.open_activity_log),
            ("系统设置", self.open_settings),
            ("数据库备份", self.backup_database)
        ]
        
        for text, command in quick_actions:
            ttk.Button(right_frame, text=text, command=command, 
                      width=20).pack(pady=2, padx=5)
    
    def create_monitoring_tab(self):
        """创建监控标签页"""
        monitoring_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitoring_frame, text="实时监控")

        # 监控工具栏
        monitor_toolbar = ttk.Frame(monitoring_frame)
        monitor_toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(monitor_toolbar, text="打开监控窗口", command=self.open_monitoring).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(monitor_toolbar, text="刷新数据", command=self.refresh_monitoring).pack(side=tk.LEFT, padx=(0, 5))

        # 系统状态框架
        status_frame = ttk.LabelFrame(monitoring_frame, text="系统状态", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 系统状态标签
        self.cpu_status_label = ttk.Label(status_frame, text="CPU使用率: --")
        self.cpu_status_label.pack(anchor=tk.W)

        self.memory_status_label = ttk.Label(status_frame, text="内存使用率: --")
        self.memory_status_label.pack(anchor=tk.W)

        self.server_status_label = ttk.Label(status_frame, text="服务器状态: --")
        self.server_status_label.pack(anchor=tk.W)

        # 在线用户列表
        users_frame = ttk.LabelFrame(monitoring_frame, text="在线用户")
        users_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建树形视图
        columns = ("用户名", "IP地址", "登录时间", "活动状态", "操作")
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings", height=8)

        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120)

        # 添加滚动条
        users_scrollbar = ttk.Scrollbar(users_frame, orient=tk.VERTICAL,
                                       command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)

        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 开始监控更新
        self.start_monitoring_update()
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="系统日志")

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_toolbar, text="打开日志窗口", command=self.open_log_window).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_toolbar, text="刷新", command=self.refresh_log_display).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_toolbar, text="清空显示", command=self.clear_log_display).pack(side=tk.LEFT, padx=(0, 5))

        # 日志级别过滤
        ttk.Label(log_toolbar, text="级别:").pack(side=tk.LEFT, padx=(10, 5))
        self.log_level_var = tk.StringVar(value="全部")
        log_level_combo = ttk.Combobox(log_toolbar, textvariable=self.log_level_var, width=10, state="readonly")
        log_level_combo['values'] = ("全部", "INFO", "WARNING", "ERROR", "CRITICAL")
        log_level_combo.pack(side=tk.LEFT, padx=(0, 5))
        log_level_combo.bind('<<ComboboxSelected>>', self.filter_log_display)

        # 日志显示区域
        log_display_frame = ttk.Frame(log_frame)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_display_frame, wrap=tk.WORD, state=tk.DISABLED, height=15)
        log_scrollbar = ttk.Scrollbar(log_display_frame, orient=tk.VERTICAL,
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置日志颜色
        self.log_text.tag_configure('INFO', foreground='blue')
        self.log_text.tag_configure('WARNING', foreground='orange')
        self.log_text.tag_configure('ERROR', foreground='red')
        self.log_text.tag_configure('CRITICAL', foreground='red', background='yellow')

        # 开始日志更新
        self.start_log_update()
    
    def create_notification_tab(self):
        """创建通知标签页"""
        notification_frame = ttk.Frame(self.notebook)
        self.notebook.add(notification_frame, text="系统通知")
        
        # 通知控制
        control_frame = ttk.Frame(notification_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(control_frame, text="通知内容:").pack(side=tk.LEFT)
        self.notification_entry = ttk.Entry(control_frame, width=50)
        self.notification_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="发送通知", 
                  command=self.send_notification).pack(side=tk.LEFT, padx=5)
        
        # 通知历史
        history_frame = ttk.LabelFrame(notification_frame, text="通知历史")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.notification_listbox = tk.Listbox(history_frame)
        notification_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, 
                                              command=self.notification_listbox.yview)
        self.notification_listbox.configure(yscrollcommand=notification_scrollbar.set)
        
        self.notification_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notification_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.toolbar.pack(fill=tk.X, pady=2)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.statusbar.pack(fill=tk.X, pady=2)
    
    def start_status_update(self):
        """启动状态更新线程"""
        def update_status():
            while True:
                try:
                    # 更新时间
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.time_label.config(text=current_time)

                    # 更新统计信息
                    self.update_statistics()

                    # 更新端口信息
                    self.update_port_info()

                    time.sleep(1)
                except:
                    break
        
        status_thread = threading.Thread(target=update_status, daemon=True)
        status_thread.start()
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            # 这里应该从服务器获取实际数据
            # 暂时使用模拟数据
            stats = {
                "total_users": "0",
                "online_users": "0",
                "shared_folders": "0",
                "total_files": "0",
                "total_size": "0 MB",
                "today_downloads": "0",
                "today_uploads": "0",
                "today_searches": "0"
            }
            
            for key, value in stats.items():
                if key in self.stats_labels:
                    self.stats_labels[key].config(text=value)
        except:
            pass
    
    def update_status(self, message: str, color: str = "black"):
        """更新状态栏"""
        self.status_label.config(text=message, foreground=color)
    
    # 菜单和按钮事件处理方法
    def start_server(self):
        """启动服务器"""
        self.server.start_server()
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
    
    def stop_server(self):
        """停止服务器"""
        self.server.stop_server()
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
    
    def restart_server(self):
        """重启服务器"""
        self.stop_server()
        time.sleep(1)
        self.start_server()

    def auto_start_server(self):
        """自动启动服务器"""
        try:
            if not self.server.running:
                self.start_server()
                print("✅ 服务器已自动启动")
        except Exception as e:
            print(f"❌ 自动启动服务器失败: {e}")
    
    # 功能实现方法
    def import_config(self):
        messagebox.showinfo("导入配置", "配置导入功能正在开发中...")

    def export_config(self):
        messagebox.showinfo("导出配置", "配置导出功能正在开发中...")

    def open_server_settings(self):
        messagebox.showinfo("服务器设置", "服务器设置功能正在开发中...")

    def open_user_management(self):
        """打开用户管理窗口"""
        try:
            from gui.user_management_window import UserManagementWindow
            user_mgmt = UserManagementWindow(self.root, self.server)
            user_mgmt.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开用户管理失败: {e}")

    def open_file_management(self):
        """打开文件管理窗口"""
        try:
            from gui.file_management_window import FileManagementWindow
            file_mgmt = FileManagementWindow(self.root, self.server)
            file_mgmt.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开文件管理失败: {e}")

    def open_download_records(self):
        """打开下载记录管理窗口"""
        try:
            from gui.download_records_window import DownloadRecordsWindow
            download_records = DownloadRecordsWindow(self.root, self.server)
            download_records.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开下载记录管理失败: {e}")

    def open_permission_management(self):
        messagebox.showinfo("权限管理", "权限管理功能正在开发中...")

    def open_activity_log(self):
        messagebox.showinfo("活动日志", "活动日志功能正在开发中...")

    def open_monitoring(self):
        """打开监控窗口"""
        try:
            from gui.monitoring_window import MonitoringWindow
            if not hasattr(self, 'monitoring_window') or not self.monitoring_window.window:
                self.monitoring_window = MonitoringWindow(self.root, self.server)
            else:
                self.monitoring_window.window.lift()
        except Exception as e:
            messagebox.showerror("错误", f"打开监控窗口失败: {e}")

    def open_settings(self):
        """打开设置窗口"""
        try:
            from gui.settings_window import SettingsWindow
            if not hasattr(self, 'settings_window') or not self.settings_window.window:
                self.settings_window = SettingsWindow(self.root, self.server)
            else:
                self.settings_window.window.lift()
        except Exception as e:
            messagebox.showerror("错误", f"打开设置窗口失败: {e}")

    def open_log_window(self):
        """打开日志窗口"""
        try:
            from gui.log_window import LogWindow
            if not hasattr(self, 'log_window') or not self.log_window.window:
                self.log_window = LogWindow(self.root, self.server)
            else:
                self.log_window.window.lift()
        except Exception as e:
            messagebox.showerror("错误", f"打开日志窗口失败: {e}")

    def start_log_update(self):
        """开始日志更新"""
        self.update_log_display()
        # 每5秒更新一次日志
        self.root.after(5000, self.start_log_update)

    def update_log_display(self):
        """更新日志显示"""
        try:
            # 读取最新的日志文件
            log_dir = os.path.join(os.getcwd(), 'logs')
            if not os.path.exists(log_dir):
                return

            # 找到最新的日志文件
            log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
            if not log_files:
                return

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_dir, f)))
            log_file_path = os.path.join(log_dir, latest_log)

            # 读取日志内容（只读取最后100行）
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-100:] if len(lines) > 100 else lines

            # 更新显示
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)

            for line in recent_lines:
                line = line.strip()
                if not line:
                    continue

                # 解析日志级别
                level = 'INFO'
                if ' - ERROR - ' in line:
                    level = 'ERROR'
                elif ' - WARNING - ' in line:
                    level = 'WARNING'
                elif ' - CRITICAL - ' in line:
                    level = 'CRITICAL'

                # 应用过滤器
                filter_level = self.log_level_var.get()
                if filter_level != "全部" and level != filter_level:
                    continue

                # 插入文本并应用颜色
                self.log_text.insert(tk.END, line + '\n', level)

            self.log_text.config(state=tk.DISABLED)
            self.log_text.see(tk.END)  # 滚动到底部

        except Exception as e:
            print(f"更新日志显示失败: {e}")

    def refresh_log_display(self):
        """刷新日志显示"""
        self.update_log_display()

    def clear_log_display(self):
        """清空日志显示"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def filter_log_display(self, event=None):
        """过滤日志显示"""
        self.update_log_display()

    def start_monitoring_update(self):
        """开始监控更新"""
        self.update_monitoring_display()
        # 每10秒更新一次监控数据
        self.root.after(10000, self.start_monitoring_update)

    def update_monitoring_display(self):
        """更新监控显示"""
        try:
            # 更新系统状态
            import psutil

            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_status_label.config(text=f"CPU使用率: {cpu_percent:.1f}%")

            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_status_label.config(text=f"内存使用率: {memory.percent:.1f}%")

            # 服务器状态
            server_status = "运行中" if self.server.running else "已停止"
            self.server_status_label.config(text=f"服务器状态: {server_status}")

            # 更新在线用户（模拟数据）
            self.update_online_users()

        except Exception as e:
            print(f"更新监控显示失败: {e}")

    def update_online_users(self):
        """更新在线用户列表"""
        try:
            # 清空现有数据
            self.users_tree.delete(*self.users_tree.get_children())

            # 这里应该从服务器获取真实的在线用户数据
            # 暂时使用模拟数据
            if self.server.running:
                sample_users = [
                    ("admin", "127.0.0.1", "2025-06-07 21:00:00", "活跃", "管理员"),
                    ("user1", "192.168.1.100", "2025-06-07 20:30:00", "空闲", "普通用户"),
                ]

                for user_data in sample_users:
                    self.users_tree.insert("", tk.END, values=user_data)

        except Exception as e:
            print(f"更新在线用户失败: {e}")

    def refresh_monitoring(self):
        """刷新监控数据"""
        self.update_monitoring_display()

    def update_port_info(self):
        """更新端口信息显示"""
        try:
            if hasattr(self.server, 'settings'):
                api_port = self.server.settings.get('server.port', 8081)
                frontend_port = self.server.settings.get('server.frontend_port', 8082)

                # 检查API服务器是否运行以及实际端口
                if hasattr(self.server, 'api_server') and self.server.api_server:
                    if hasattr(self.server.api_server, 'running') and self.server.api_server.running:
                        self.port_label.config(
                            text=f"API端口: {api_port} | 前端端口: {frontend_port} (运行中)",
                            foreground="green"
                        )
                    else:
                        self.port_label.config(
                            text=f"API端口: {api_port} | 前端端口: {frontend_port} (已停止)",
                            foreground="red"
                        )
                else:
                    self.port_label.config(
                        text=f"API端口: {api_port} | 前端端口: {frontend_port} (未启动)",
                        foreground="orange"
                    )
            else:
                self.port_label.config(text="端口: --", foreground="gray")
        except Exception as e:
            print(f"更新端口信息失败: {e}")
            self.port_label.config(text="端口: --", foreground="gray")

    def backup_database(self):
        """备份数据库"""
        try:
            if self.server.db_manager:
                import os
                from datetime import datetime

                backup_dir = "./backup"
                os.makedirs(backup_dir, exist_ok=True)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"{backup_dir}/backup_{timestamp}.sql"

                self.server.db_manager.backup_database(backup_file)
                messagebox.showinfo("成功", f"数据库备份成功: {backup_file}")
            else:
                messagebox.showerror("错误", "数据库管理器不可用")
        except Exception as e:
            messagebox.showerror("错误", f"数据库备份失败: {e}")

    def restore_database(self):
        """恢复数据库"""
        try:
            from tkinter import filedialog

            if self.server.db_manager:
                backup_file = filedialog.askopenfilename(
                    title="选择备份文件",
                    filetypes=[("SQL文件", "*.sql"), ("所有文件", "*.*")]
                )

                if backup_file:
                    if messagebox.askyesno("确认", "确定要恢复数据库吗？\n此操作将覆盖现有数据！"):
                        self.server.db_manager.restore_database(backup_file)
                        messagebox.showinfo("成功", "数据库恢复成功")
            else:
                messagebox.showerror("错误", "数据库管理器不可用")
        except Exception as e:
            messagebox.showerror("错误", f"数据库恢复失败: {e}")

    def clear_cache(self):
        """清理缓存"""
        try:
            # 清理缩略图缓存
            thumbnail_service = self.server.services.get('thumbnail')
            if thumbnail_service:
                thumbnail_service.cleanup_orphaned_thumbnails()

            # 清理下载包缓存
            encryption_service = self.server.services.get('encryption')
            if encryption_service:
                encryption_service.cleanup_expired_packages()

            messagebox.showinfo("成功", "缓存清理完成")
        except Exception as e:
            messagebox.showerror("错误", f"清理缓存失败: {e}")

    def system_diagnosis(self):
        """系统诊断"""
        try:
            diagnosis_info = []

            # 检查服务状态
            diagnosis_info.append("=== 服务状态 ===")
            for service_name, service in self.server.services.items():
                status = "运行中" if service else "未运行"
                diagnosis_info.append(f"{service_name}: {status}")

            # 检查数据库连接
            diagnosis_info.append("\n=== 数据库状态 ===")
            if self.server.db_manager:
                try:
                    self.server.db_manager.test_connection()
                    diagnosis_info.append("数据库连接: 正常")
                except:
                    diagnosis_info.append("数据库连接: 异常")
            else:
                diagnosis_info.append("数据库连接: 不可用")

            # 显示诊断结果
            diagnosis_text = "\n".join(diagnosis_info)

            # 创建诊断窗口
            diag_window = tk.Toplevel(self.root)
            diag_window.title("系统诊断")
            diag_window.geometry("500x400")

            text_widget = tk.Text(diag_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, diagnosis_text)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"系统诊断失败: {e}")

    def show_manual(self):
        """显示使用手册"""
        try:
            import webbrowser
            import os

            manual_file = os.path.join(os.path.dirname(__file__), "..", "使用指南.md")
            if os.path.exists(manual_file):
                webbrowser.open(f"file://{os.path.abspath(manual_file)}")
            else:
                messagebox.showinfo("使用手册", "使用手册文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开使用手册失败: {e}")

    def show_about(self):
        """显示关于信息"""
        about_text = """企业级文件共享系统 v1.0.0

功能特性:
• 权限管理和用户控制
• 双搜索引擎（文本+图像）
• 缩略图支持
• 加密下载保护
• 实时监控和日志
• 滚动通知系统

技术架构:
• Python 3.7+
• MySQL 数据库
• Flask API服务
• tkinter GUI界面

开发团队: 系统开发团队
版权所有 © 2024"""

        messagebox.showinfo("关于", about_text)

    def add_shared_folder(self):
        """添加共享文件夹"""
        try:
            from tkinter import filedialog

            folder_path = filedialog.askdirectory(title="选择要共享的文件夹")
            if folder_path:
                # 这里应该调用文件服务添加共享文件夹
                file_service = self.server.services.get('file')
                if file_service:
                    folder_name = os.path.basename(folder_path)
                    result = file_service.create_shared_folder(folder_name, folder_path)

                    if result.get('success', False):
                        messagebox.showinfo("成功", f"共享文件夹添加成功: {folder_name}")
                    else:
                        messagebox.showerror("错误", f"添加共享文件夹失败: {result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "文件服务不可用")
        except Exception as e:
            messagebox.showerror("错误", f"添加共享文件夹失败: {e}")

    def create_user(self):
        """创建用户"""
        self.open_user_management()

    def send_notification(self):
        """发送通知"""
        try:
            notification_text = self.notification_entry.get().strip()
            if notification_text:
                # 添加到通知历史
                timestamp = time.strftime("%H:%M:%S")
                notification_item = f"[{timestamp}] {notification_text}"
                self.notification_listbox.insert(0, notification_item)

                # 清空输入框
                self.notification_entry.delete(0, tk.END)

                # 如果有API服务器，广播通知
                if self.server.api_server:
                    self.server.api_server.broadcast_notification(notification_text)

                messagebox.showinfo("成功", "通知发送成功")
            else:
                messagebox.showwarning("警告", "通知内容不能为空")
        except Exception as e:
            messagebox.showerror("错误", f"发送通知失败: {e}")

    def create_marquee_tab(self):
        """创建滚动字幕标签页"""
        marquee_frame = ttk.Frame(self.notebook)
        self.notebook.add(marquee_frame, text="滚动字幕")
        
        # 字幕控制
        control_frame = ttk.LabelFrame(marquee_frame, text="字幕设置")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 字幕内容
        ttk.Label(control_frame, text="字幕内容:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.marquee_text = tk.Text(control_frame, width=60, height=3)
        self.marquee_text.grid(row=0, column=1, columnspan=2, padx=5, pady=5)
        
        # 主题选择
        ttk.Label(control_frame, text="主题样式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.marquee_theme = ttk.Combobox(control_frame, values=['默认', '信息', '成功', '警告', '错误'], state='readonly')
        self.marquee_theme.set('默认')
        self.marquee_theme.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 启用/隐藏选项
        self.marquee_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="启用字幕", variable=self.marquee_enabled).grid(row=1, column=2, padx=5, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="更新字幕", command=self.update_marquee).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="加载当前", command=self.load_current_marquee).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="隐藏字幕", command=self.hide_marquee).pack(side=tk.LEFT, padx=5)
        
        # 预设消息
        preset_frame = ttk.LabelFrame(marquee_frame, text="预设消息")
        preset_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        preset_messages = [
            "欢迎使用企业级文件共享系统！",
            "系统维护通知：今晚22:00-24:00进行系统升级维护",
            "新功能上线：支持PSD、AI等专业格式预览",
            "重要提醒：请及时清理个人下载记录",
            "系统公告：为保障系统安全，请勿下载可疑文件"
        ]
        
        for i, msg in enumerate(preset_messages):
            btn = ttk.Button(preset_frame, text=f"预设{i+1}", 
                           command=lambda m=msg: self.set_preset_message(m))
            btn.grid(row=i//2, column=i%2, padx=5, pady=2, sticky=tk.W)
        
        # 加载当前字幕设置
        self.load_current_marquee()
    
    def update_marquee(self):
        """更新滚动字幕"""
        try:
            message = self.marquee_text.get(1.0, tk.END).strip()
            if not message:
                messagebox.showwarning("警告", "字幕内容不能为空")
                return
            
            theme_map = {'默认': '', '信息': 'info', '成功': 'success', '警告': 'warning', '错误': 'error'}
            theme = theme_map.get(self.marquee_theme.get(), '')
            enabled = self.marquee_enabled.get()
            
            # 更新配置
            if hasattr(self.server, 'settings'):
                self.server.settings.settings.setdefault('marquee', {})
                self.server.settings.settings['marquee'].update({
                    'message': message,
                    'theme': theme,
                    'enabled': enabled,
                    'hidden': not enabled
                })
                self.server.settings.save_settings()
            
            # 如果有API服务器，广播更新
            if hasattr(self.server, 'api_server') and self.server.api_server:
                self.server.api_server.socketio.emit('marquee_update', {
                    'message': message,
                    'theme': theme,
                    'enabled': enabled,
                    'hidden': not enabled
                })
            
            messagebox.showinfo("成功", "滚动字幕更新成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"更新滚动字幕失败: {e}")
    
    def load_current_marquee(self):
        """加载当前字幕设置"""
        try:
            if hasattr(self.server, 'settings'):
                marquee_config = self.server.settings.settings.get('marquee', {})
                
                # 设置文本
                message = marquee_config.get('message', '欢迎使用企业级文件共享系统！')
                self.marquee_text.delete(1.0, tk.END)
                self.marquee_text.insert(1.0, message)
                
                # 设置主题
                theme = marquee_config.get('theme', '')
                theme_map = {'': '默认', 'info': '信息', 'success': '成功', 'warning': '警告', 'error': '错误'}
                self.marquee_theme.set(theme_map.get(theme, '默认'))
                
                # 设置启用状态
                enabled = marquee_config.get('enabled', True)
                self.marquee_enabled.set(enabled)
                
        except Exception as e:
            messagebox.showerror("错误", f"加载字幕设置失败: {e}")
    
    def hide_marquee(self):
        """隐藏滚动字幕"""
        try:
            # 更新配置
            if hasattr(self.server, 'settings'):
                self.server.settings.settings.setdefault('marquee', {})
                self.server.settings.settings['marquee']['hidden'] = True
                self.server.settings.save_settings()
            
            # 广播隐藏
            if hasattr(self.server, 'api_server') and self.server.api_server:
                self.server.api_server.socketio.emit('marquee_update', {
                    'hidden': True
                })
            
            messagebox.showinfo("成功", "滚动字幕已隐藏")
            
        except Exception as e:
            messagebox.showerror("错误", f"隐藏字幕失败: {e}")
    
    def set_preset_message(self, message):
        """设置预设消息"""
        self.marquee_text.delete(1.0, tk.END)
        self.marquee_text.insert(1.0, message)
