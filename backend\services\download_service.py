#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载服务 - 处理文件下载和加密
"""

import os
import zipfile
import tempfile
import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import shutil
from sqlalchemy import func

from utils.logger import setup_logger
from models.file_share import SharedFile, SharedFolder
from models.download_record import DownloadRecord, DownloadStatistics, PasswordRequest

class DownloadService:
    """下载服务类"""

    def __init__(self, db_manager, config=None):
        self.db_manager = db_manager
        self.logger = setup_logger("DownloadService")
        self.temp_dir = os.path.join(os.getcwd(), "temp", "downloads")
        self.ensure_temp_dir()

        # 加载配置
        self.config = config or {}
        download_config = self.config.get('download', {})

        # 下载配置
        self.encryption_threshold = download_config.get('encryption_after_downloads', 3)
        self.password_request_limit = download_config.get('password_request_limit', 5)
        self.password_length = 12  # 密码长度

        self.logger.info(f"下载服务初始化完成 - 加密阈值: {self.encryption_threshold}, 密码申请限制: {self.password_request_limit}")
    
    def ensure_temp_dir(self):
        """确保临时目录存在"""
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir, exist_ok=True)
    
    def generate_password(self, length: int = None) -> str:
        """生成随机密码"""
        if length is None:
            length = self.password_length

        # 确保密码包含大小写字母、数字和特殊字符
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"

        # 至少包含每种字符一个
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]

        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))

        # 随机打乱
        secrets.SystemRandom().shuffle(password)
        return ''.join(password)
    
    def should_encrypt_file(self, file_id: int, user_id: int = None) -> bool:
        """检查文件是否需要加密（基于数据库下载记录）"""
        try:
            with self.db_manager.get_session() as session:
                # 获取文件的下载统计
                stats = session.query(DownloadStatistics).filter_by(file_id=file_id).first()
                if not stats:
                    # 如果没有统计记录，创建一个
                    stats = DownloadStatistics(file_id=file_id)
                    session.add(stats)
                    session.commit()
                    return False

                # 检查总下载次数是否超过阈值
                total_downloads = stats.total_downloads
                self.logger.debug(f"文件 {file_id} 总下载次数: {total_downloads}, 加密阈值: {self.encryption_threshold}")

                return total_downloads >= self.encryption_threshold

        except Exception as e:
            self.logger.error(f"检查文件加密状态失败: {e}")
            return False

    def record_download(self, user_id: int = None, download_type: str = 'single',
                       zip_path: str = None, is_encrypted: bool = False, password: str = None,
                       file_id: int = None, folder_id: int = None, batch_id: str = None,
                       session_id: str = None, ip_address: str = None, user_agent: str = None,
                       download_source: str = 'web', request_context: dict = None) -> bool:
        """记录下载到数据库（增强版）"""
        try:
            with self.db_manager.get_session() as session:
                # 对于文件下载，更新下载统计
                if file_id and download_type in ['single', 'batch']:
                    stats = session.query(DownloadStatistics).filter_by(file_id=file_id).first()
                    if not stats:
                        stats = DownloadStatistics(file_id=file_id)
                        session.add(stats)

                    # 增加下载次数
                    stats.increment_download(is_encrypted=is_encrypted)

                # 创建下载记录
                if zip_path and os.path.exists(zip_path):
                    file_size = os.path.getsize(zip_path)
                    zip_filename = os.path.basename(zip_path)

                    # 获取批次ID（如果提供了批次对象）
                    batch_db_id = None
                    if batch_id:
                        from models.download_batch import DownloadBatch
                        batch = session.query(DownloadBatch).filter_by(batch_id=batch_id).first()
                        if batch:
                            batch_db_id = batch.id

                    download_record = DownloadRecord(
                        download_type=download_type,
                        zip_filename=zip_filename,
                        zip_path=zip_path,
                        file_size=file_size,
                        file_id=file_id,
                        folder_id=folder_id,
                        user_id=user_id,
                        batch_id=batch_db_id,
                        session_id=session_id,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        download_source=download_source,
                        is_encrypted=is_encrypted,
                        password=password,
                        download_status='completed',
                        downloaded_at=datetime.now()
                    )
                    session.add(download_record)

                    # 更新用户下载活动统计
                    self._update_user_activity(session, user_id, download_type, file_size, 
                                             is_encrypted, session_id, ip_address, 
                                             download_source, file_id)

                session.commit()
                target_id = file_id if file_id else folder_id
                target_type = "文件" if file_id else "文件夹"
                self.logger.info(f"记录下载成功: {target_type}ID={target_id}, 用户ID={user_id}, 类型={download_type}, 批次={batch_id}, 加密={is_encrypted}")
                return True

        except Exception as e:
            self.logger.error(f"记录下载失败: {e}")
            return False
    
    def create_download_batch(self, batch_type: str, target_type: str, download_name: str,
                            target_id: int = None, user_id: int = None, 
                            request_context: dict = None) -> str:
        """创建下载批次记录"""
        try:
            from models.download_batch import DownloadBatch
            
            # 从请求上下文中提取信息
            session_id = request_context.get('session_id') if request_context else None
            ip_address = request_context.get('ip_address') if request_context else None
            user_agent = request_context.get('user_agent') if request_context else None
            download_source = request_context.get('download_source', 'web') if request_context else 'web'
            
            with self.db_manager.get_session() as session:
                batch = DownloadBatch(
                    batch_type=batch_type,
                    target_type=target_type,
                    download_name=download_name,
                    target_id=target_id,
                    user_id=user_id,
                    session_id=session_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    download_source=download_source
                )
                
                session.add(batch)
                session.commit()
                
                self.logger.info(f"创建下载批次成功: {batch.batch_id}, 类型={batch_type}, 目标={target_type}:{target_id}")
                return batch.batch_id
                
        except Exception as e:
            self.logger.error(f"创建下载批次失败: {e}")
            return None
    
    def update_batch_status(self, batch_id: str, status: str, **kwargs):
        """更新批次状态"""
        try:
            from models.download_batch import DownloadBatch
            
            with self.db_manager.get_session() as session:
                batch = session.query(DownloadBatch).filter_by(batch_id=batch_id).first()
                if batch:
                    batch.status = status
                    
                    # 更新其他字段
                    for key, value in kwargs.items():
                        if hasattr(batch, key):
                            setattr(batch, key, value)
                    
                    # 根据状态更新时间
                    if status == 'downloading':
                        batch.start_download()
                    elif status == 'completed':
                        batch.complete_download()
                    elif status == 'failed':
                        error_message = kwargs.get('error_message', '未知错误')
                        batch.fail_download(error_message)
                    elif status == 'ready':
                        batch.mark_ready()
                    
                    session.commit()
                    self.logger.info(f"批次状态更新成功: {batch_id} -> {status}")
                    return True
                else:
                    self.logger.warning(f"批次不存在: {batch_id}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"更新批次状态失败: {e}")
            return False
    
    def _update_user_activity(self, session, user_id: int, download_type: str, 
                            file_size: int, is_encrypted: bool, session_id: str,
                            ip_address: str, download_source: str, file_id: int = None):
        """更新用户下载活动统计"""
        try:
            from models.user_download_activity import UserDownloadActivity
            from datetime import date
            
            today = date.today()
            
            # 获取或创建今日活动记录
            activity = session.query(UserDownloadActivity).filter_by(
                user_id=user_id, activity_date=today
            ).first()
            
            if not activity:
                activity = UserDownloadActivity(user_id=user_id, activity_date=today)
                session.add(activity)
            
            # 获取文件扩展名
            file_extension = None
            if file_id:
                from models.file_share import SharedFile
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if file_record and file_record.filename:
                    file_extension = os.path.splitext(file_record.filename)[1][1:]  # 去掉点号
            
            # 更新活动统计
            activity.add_download(
                download_type=download_type,
                file_size=file_size,
                file_extension=file_extension,
                is_encrypted=is_encrypted,
                session_id=session_id,
                ip_address=ip_address,
                download_source=download_source
            )
            
            self.logger.debug(f"用户活动统计更新成功: 用户={user_id}, 日期={today}")
            
        except Exception as e:
            self.logger.error(f"更新用户活动统计失败: {e}")
    
    def create_zip_file(self, files: List[Dict[str, Any]], zip_name: str,
                       password: Optional[str] = None) -> Dict[str, Any]:
        """创建压缩文件"""
        try:
            zip_path = os.path.join(self.temp_dir, f"{zip_name}.zip")

            if password:
                # 使用密码保护创建ZIP文件
                return self._create_encrypted_zip(files, zip_path, password)
            else:
                # 创建普通ZIP文件
                return self._create_normal_zip(files, zip_path)

        except Exception as e:
            self.logger.error(f"创建压缩文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_normal_zip(self, files: List[Dict[str, Any]], zip_path: str) -> Dict[str, Any]:
        """创建普通ZIP文件"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_info in files:
                    file_path = file_info['full_path']
                    archive_name = file_info['archive_name']

                    if os.path.exists(file_path):
                        zipf.write(file_path, archive_name)
                    else:
                        self.logger.warning(f"文件不存在: {file_path}")

            if os.path.exists(zip_path):
                file_size = os.path.getsize(zip_path)
                return {
                    "success": True,
                    "zip_path": zip_path,
                    "file_size": file_size,
                    "password": None
                }
            else:
                return {"success": False, "error": "压缩文件创建失败"}

        except Exception as e:
            self.logger.error(f"创建普通压缩文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_encrypted_zip(self, files: List[Dict[str, Any]], zip_path: str, password: str) -> Dict[str, Any]:
        """创建加密ZIP文件"""
        try:
            # 尝试使用pyminizip创建加密ZIP
            try:
                import pyminizip

                # 准备文件列表
                file_paths = []
                archive_names = []

                for file_info in files:
                    file_path = file_info['full_path']
                    archive_name = file_info['archive_name']

                    if os.path.exists(file_path):
                        # 使用原路径，pyminizip应该能处理UTF-8编码的路径
                        file_paths.append(file_path)
                        archive_names.append(archive_name)
                    else:
                        self.logger.warning(f"文件不存在: {file_path}")

                if not file_paths:
                    return {"success": False, "error": "没有有效的文件可压缩"}

                # 创建加密ZIP文件
                compression_level = 5  # 压缩级别 0-9

                try:
                    # 如果只有一个文件，使用单文件压缩方法
                    if len(file_paths) == 1:
                        pyminizip.compress(
                            file_paths[0],       # 源文件路径
                            archive_names[0],    # 压缩包内文件名
                            zip_path,           # 输出ZIP文件路径
                            password,           # 密码
                            compression_level   # 压缩级别
                        )
                    else:
                        # 多文件压缩
                        pyminizip.compress_multiple(
                            file_paths,           # 源文件路径列表
                            archive_names,        # 压缩包内文件名列表
                            zip_path,            # 输出ZIP文件路径
                            password,            # 密码
                            compression_level    # 压缩级别
                        )

                    if os.path.exists(zip_path):
                        file_size = os.path.getsize(zip_path)
                        self.logger.info(f"成功创建加密ZIP文件: {zip_path}, 密码: {password}")
                        return {
                            "success": True,
                            "zip_path": zip_path,
                            "file_size": file_size,
                            "password": password
                        }
                    else:
                        return {"success": False, "error": "加密压缩文件创建失败"}

                except Exception as pyminizip_error:
                    self.logger.error(f"pyminizip压缩失败: {pyminizip_error}")
                    # 如果pyminizip失败（通常是中文路径问题），使用备用方案
                    return self._create_encrypted_zip_fallback(files, zip_path, password)

            except ImportError:
                # 如果pyminizip不可用，使用备用方案
                self.logger.warning("pyminizip不可用，使用备用加密方案")
                return self._create_encrypted_zip_fallback(files, zip_path, password)

        except Exception as e:
            self.logger.error(f"创建加密压缩文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_encrypted_zip_fallback(self, files: List[Dict[str, Any]], zip_path: str, password: str) -> Dict[str, Any]:
        """备用加密ZIP创建方案"""
        try:
            # 先创建普通ZIP文件
            temp_zip = zip_path + ".temp"
            result = self._create_normal_zip(files, temp_zip)

            if not result.get('success'):
                return result

            # 尝试多种加密方案

            # 方案1: 使用7zip命令行工具（如果可用）
            try:
                import subprocess

                # 尝试使用7zip添加密码
                cmd = [
                    '7z', 'a', '-p' + password, '-tzip', '-mem=AES256',
                    zip_path
                ]

                # 添加所有文件
                for file_info in files:
                    if os.path.exists(file_info['full_path']):
                        cmd.append(file_info['full_path'])

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0 and os.path.exists(zip_path):
                    # 删除临时文件
                    if os.path.exists(temp_zip):
                        os.remove(temp_zip)

                    file_size = os.path.getsize(zip_path)
                    self.logger.info(f"使用7zip成功创建加密ZIP文件: {zip_path}")
                    return {
                        "success": True,
                        "zip_path": zip_path,
                        "file_size": file_size,
                        "password": password
                    }
                else:
                    self.logger.warning("7zip加密失败，尝试其他方案")

            except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
                self.logger.warning(f"7zip不可用: {e}，尝试其他方案")

            # 方案2: 使用zipfile库创建带密码的ZIP（Python 3.7+）
            try:
                import zipfile

                # 删除临时文件，重新创建加密ZIP
                if os.path.exists(temp_zip):
                    os.remove(temp_zip)

                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 设置密码（注意：这只是设置了密码，但zipfile库的加密较弱）
                    zipf.setpassword(password.encode('utf-8'))

                    for file_info in files:
                        file_path = file_info['full_path']
                        archive_name = file_info['archive_name']

                        if os.path.exists(file_path):
                            zipf.write(file_path, archive_name)

                if os.path.exists(zip_path):
                    file_size = os.path.getsize(zip_path)
                    self.logger.info(f"使用zipfile成功创建加密ZIP文件: {zip_path}")
                    return {
                        "success": True,
                        "zip_path": zip_path,
                        "file_size": file_size,
                        "password": password
                    }

            except Exception as e:
                self.logger.warning(f"zipfile加密失败: {e}，使用标记方案")

            # 方案3: 标记方案（创建普通ZIP但在数据库中标记为需要密码）
            return self._create_marked_zip(files, zip_path, password, temp_zip)

        except Exception as e:
            self.logger.error(f"备用加密方案失败: {e}")
            return {"success": False, "error": str(e)}

    def _create_marked_zip(self, files: List[Dict[str, Any]], zip_path: str, password: str, temp_zip: str) -> Dict[str, Any]:
        """创建标记的ZIP文件（包含密码信息但不真正加密）"""
        try:
            # 重命名临时文件为最终文件
            if os.path.exists(temp_zip):
                shutil.move(temp_zip, zip_path)

            if os.path.exists(zip_path):
                file_size = os.path.getsize(zip_path)
                self.logger.warning(f"创建标记ZIP文件（未真正加密）: {zip_path}, 密码: {password}")
                return {
                    "success": True,
                    "zip_path": zip_path,
                    "file_size": file_size,
                    "password": password,
                    "encryption_note": "文件未真正加密，仅在数据库中标记为需要密码"
                }
            else:
                return {"success": False, "error": "标记压缩文件创建失败"}

        except Exception as e:
            self.logger.error(f"创建标记ZIP文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_single_file_download(self, file_id: int, user_id: int = None, 
                                   request_context: dict = None) -> Dict[str, Any]:
        """准备单文件下载"""
        try:
            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if not file_record:
                    return {"success": False, "error": "文件不存在"}
                
                folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                # 获取文件完整路径
                full_path = os.path.join(folder.path, file_record.relative_path)
                if not os.path.exists(full_path):
                    return {"success": False, "error": "文件不存在于磁盘"}
                
                # 检查是否需要加密
                needs_encryption = self.should_encrypt_file(file_id, user_id)
                password = None

                if needs_encryption:
                    password = self.generate_password()
                    self.logger.info(f"文件 {file_record.filename} 需要加密下载")

                # 准备压缩文件信息
                files_to_zip = [{
                    'full_path': full_path,
                    'archive_name': file_record.filename
                }]

                # 生成压缩文件名
                zip_name = f"{file_record.filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)

                if zip_result.get('success'):
                    # 创建下载批次
                    batch_id = self.create_download_batch(
                        batch_type='single',
                        target_type='file',
                        download_name=file_record.filename,
                        target_id=file_id,
                        user_id=user_id,
                        request_context=request_context
                    )
                    
                    # 更新批次信息
                    if batch_id:
                        self.update_batch_status(batch_id, 'ready', 
                                               total_files=1, 
                                               total_size=os.path.getsize(full_path),
                                               compressed_size=zip_result['file_size'],
                                               is_encrypted=needs_encryption,
                                               password_required=needs_encryption)
                    
                    # 记录下载到数据库
                    self.record_download(
                        user_id=user_id,
                        download_type='single',
                        zip_path=zip_result['zip_path'],
                        is_encrypted=needs_encryption,
                        password=password,
                        file_id=file_id,
                        folder_id=None,
                        batch_id=batch_id,
                        session_id=request_context.get('session_id') if request_context else None,
                        ip_address=request_context.get('ip_address') if request_context else None,
                        user_agent=request_context.get('user_agent') if request_context else None,
                        download_source=request_context.get('download_source', 'web') if request_context else 'web',
                        request_context=request_context
                    )

                    # 记录下载信息
                    download_info = {
                        "success": True,
                        "file_id": file_id,
                        "filename": file_record.filename,
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": needs_encryption,
                        "password": password,
                        "batch_id": batch_id
                    }

                    # 如果加密，保存密码申请记录
                    if needs_encryption and password:
                        self.save_password_request(file_id, password, user_id)

                    return download_info
                else:
                    return zip_result
                
        except Exception as e:
            self.logger.error(f"准备文件下载失败: {e}")
            return {"success": False, "error": str(e)}
    
    def prepare_batch_download(self, file_ids: List[int], user_id: int = None, 
                             request_context: dict = None) -> Dict[str, Any]:
        """准备批量文件下载"""
        try:
            # 检查批量下载文件数量限制
            max_batch_files = self.config.get('download', {}).get('max_batch_files', 100)
            if len(file_ids) > max_batch_files:
                return {
                    "success": False,
                    "error": f"批量下载文件数量超过限制，最多允许 {max_batch_files} 个文件"
                }

            with self.db_manager.get_session() as session:
                files_to_zip = []
                encrypted_files = []
                total_downloads = 0
                total_size = 0

                for file_id in file_ids:
                    file_record = session.query(SharedFile).filter_by(id=file_id).first()
                    if not file_record:
                        continue

                    folder = session.query(SharedFolder).filter_by(id=file_record.folder_id).first()
                    if not folder:
                        continue

                    full_path = os.path.join(folder.path, file_record.relative_path)
                    if not os.path.exists(full_path):
                        continue

                    # 检查文件大小
                    file_size = os.path.getsize(full_path)
                    total_size += file_size

                    # 检查总大小限制
                    max_package_size = self.config.get('download', {}).get('max_package_size', 500 * 1024 * 1024)
                    if total_size > max_package_size:
                        return {
                            "success": False,
                            "error": f"批量下载总大小超过限制，最大允许 {max_package_size // (1024*1024)} MB"
                        }

                    files_to_zip.append({
                        'full_path': full_path,
                        'archive_name': f"{folder.name}/{file_record.relative_path}"
                    })

                    # 检查是否有文件需要加密
                    if self.should_encrypt_file(file_id, user_id):
                        encrypted_files.append(file_id)

                    total_downloads += 1
                
                if not files_to_zip:
                    return {"success": False, "error": "没有有效的文件可下载"}
                
                # 如果有加密文件，整个压缩包都加密
                password = None
                if encrypted_files:
                    password = self.generate_password()
                    self.logger.info(f"批量下载包含 {len(encrypted_files)} 个需要加密的文件")
                
                # 生成压缩文件名
                zip_name = f"batch_download_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)

                if zip_result.get('success'):
                    # 创建下载批次
                    batch_id = self.create_download_batch(
                        batch_type='batch',
                        target_type='file',
                        download_name=f"批量下载_{len(file_ids)}个文件",
                        user_id=user_id,
                        request_context=request_context
                    )
                    
                    # 更新批次信息
                    if batch_id:
                        self.update_batch_status(batch_id, 'ready',
                                               total_files=len(files_to_zip),
                                               total_size=total_size,
                                               compressed_size=zip_result['file_size'],
                                               is_encrypted=bool(encrypted_files),
                                               password_required=bool(encrypted_files))
                    
                    # 记录每个文件的下载
                    for file_id in file_ids:
                        if any(f['full_path'] for f in files_to_zip if str(file_id) in f['archive_name']):
                            self.record_download(
                                file_id=file_id,
                                user_id=user_id,
                                download_type='batch',
                                zip_path=zip_result['zip_path'],
                                is_encrypted=bool(encrypted_files),
                                password=password,
                                batch_id=batch_id,
                                session_id=request_context.get('session_id') if request_context else None,
                                ip_address=request_context.get('ip_address') if request_context else None,
                                user_agent=request_context.get('user_agent') if request_context else None,
                                download_source=request_context.get('download_source', 'web') if request_context else 'web',
                                request_context=request_context
                            )

                    download_info = {
                        "success": True,
                        "file_count": len(files_to_zip),
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": bool(encrypted_files),
                        "password": password,
                        "encrypted_file_ids": encrypted_files,
                        "batch_id": batch_id
                    }

                    # 如果加密，保存密码申请记录
                    if password:
                        for file_id in encrypted_files:
                            self.save_password_request(file_id, password, user_id)

                    return download_info
                else:
                    return zip_result
                
        except Exception as e:
            self.logger.error(f"准备批量下载失败: {e}")
            return {"success": False, "error": str(e)}

    def prepare_folder_download(self, folder_id: int, user_id: int = None,
                              request_context: dict = None) -> Dict[str, Any]:
        """准备文件夹下载"""
        try:
            self.logger.info(f"准备文件夹下载: folder_id={folder_id}, user_id={user_id}, request_context={request_context}")
            with self.db_manager.get_session() as session:
                # 获取文件夹信息
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}

                if not os.path.exists(folder.path):
                    return {"success": False, "error": "文件夹路径不存在"}

                # 获取文件夹中的所有文件
                files = session.query(SharedFile).filter_by(folder_id=folder_id).all()

                files_to_zip = []
                encrypted_files = []
                total_downloads = 0

                for file_record in files:
                    full_path = os.path.join(folder.path, file_record.relative_path)
                    if not os.path.exists(full_path):
                        continue

                    files_to_zip.append({
                        'full_path': full_path,
                        'archive_name': file_record.relative_path
                    })

                    # 检查是否有文件需要加密
                    if self.should_encrypt_file(file_record.id, user_id):
                        encrypted_files.append(file_record.id)

                    total_downloads += 1

                if not files_to_zip:
                    return {"success": False, "error": "文件夹中没有有效的文件"}

                # 如果有加密文件，整个压缩包都加密
                password = None
                if encrypted_files:
                    password = self.generate_password()
                    self.logger.info(f"文件夹下载包含 {len(encrypted_files)} 个需要加密的文件")

                # 生成压缩文件名
                zip_name = f"folder_{folder.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                # 创建压缩文件
                zip_result = self.create_zip_file(files_to_zip, zip_name, password)

                if zip_result.get('success'):
                    # 创建下载批次
                    batch_id = self.create_download_batch(
                        batch_type='folder',
                        target_type='folder',
                        download_name=f"文件夹_{folder.name}",
                        target_id=folder_id,
                        user_id=user_id,
                        request_context=request_context
                    )
                    
                    # 更新批次信息
                    if batch_id:
                        self.update_batch_status(batch_id, 'ready',
                                               total_files=len(files_to_zip),
                                               total_size=sum(f.get('size', 0) for f in files_to_zip),
                                               compressed_size=zip_result['file_size'],
                                               is_encrypted=bool(encrypted_files),
                                               password_required=bool(encrypted_files))
                    
                    # 为整个文件夹创建一条下载记录
                    self.record_download(
                        user_id=user_id,
                        download_type='folder',
                        zip_path=zip_result['zip_path'],
                        is_encrypted=bool(encrypted_files),
                        password=password,
                        file_id=None,
                        folder_id=folder_id,
                        batch_id=batch_id,
                        session_id=request_context.get('session_id') if request_context else None,
                        ip_address=request_context.get('ip_address') if request_context else None,
                        user_agent=request_context.get('user_agent') if request_context else None,
                        download_source=request_context.get('download_source', 'web') if request_context else 'web',
                        request_context=request_context
                    )

                    download_info = {
                        "success": True,
                        "file_count": len(files_to_zip),
                        "zip_path": zip_result['zip_path'],
                        "file_size": zip_result['file_size'],
                        "is_encrypted": bool(encrypted_files),
                        "password": password,
                        "encrypted_file_ids": encrypted_files,
                        "folder_name": folder.name,
                        "batch_id": batch_id
                    }

                    # 如果加密，保存密码申请记录
                    if password:
                        for file_id in encrypted_files:
                            self.save_password_request(file_id, password, user_id)

                    return download_info
                else:
                    return zip_result

        except Exception as e:
            self.logger.error(f"准备文件夹下载失败: {e}")
            return {"success": False, "error": str(e)}

    def save_password_request(self, file_id: int, password: str, user_id: int = None):
        """保存密码申请记录到数据库"""
        try:
            with self.db_manager.get_session() as session:
                # 创建密码申请记录
                password_request = PasswordRequest(
                    file_id=file_id,
                    user_id=user_id,
                    status='approved',  # 自动批准
                    password_provided=password,
                    password_expires_at=datetime.now() + timedelta(hours=24),  # 24小时后过期
                    approved_at=datetime.now()
                )
                session.add(password_request)
                session.commit()

                self.logger.info(f"保存密码申请记录成功: 文件ID={file_id}, 用户ID={user_id}")

        except Exception as e:
            self.logger.error(f"保存密码申请记录失败: {e}")
    
    def request_password(self, file_id: int, user_id: int = None) -> Dict[str, Any]:
        """申请解压密码"""
        try:
            # 检查申请次数限制
            max_requests = 3  # 最大申请次数
            current_requests = self.get_password_request_count(file_id, user_id)
            
            if current_requests >= max_requests:
                return {
                    "success": False,
                    "error": f"密码申请次数已达上限 ({max_requests} 次)"
                }
            
            # 获取文件的当前密码
            password = self.get_file_password(file_id)
            if not password:
                return {"success": False, "error": "文件未加密或密码不存在"}
            
            # 记录申请
            self.record_password_request(file_id, user_id)
            
            return {
                "success": True,
                "password": password,
                "remaining_requests": max_requests - current_requests - 1
            }
            
        except Exception as e:
            self.logger.error(f"申请密码失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_password_request_count(self, file_id: int, user_id: int = None) -> int:
        """获取密码申请次数"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(PasswordRequest).filter_by(file_id=file_id)
                if user_id:
                    query = query.filter_by(user_id=user_id)

                count = query.count()
                return count

        except Exception as e:
            self.logger.error(f"获取密码申请次数失败: {e}")
            return 0

    def get_file_password(self, file_id: int) -> Optional[str]:
        """获取文件最新的有效密码"""
        try:
            with self.db_manager.get_session() as session:
                # 查找最新的有效密码申请记录
                password_request = session.query(PasswordRequest).filter(
                    PasswordRequest.file_id == file_id,
                    PasswordRequest.status == 'approved',
                    PasswordRequest.password_expires_at > datetime.now()
                ).order_by(PasswordRequest.approved_at.desc()).first()

                if password_request:
                    return password_request.password_provided

                return None

        except Exception as e:
            self.logger.error(f"获取文件密码失败: {e}")
            return None
    
    def record_password_request(self, file_id: int, user_id: int = None):
        """记录密码申请"""
        try:
            with self.db_manager.get_session() as session:
                # 创建密码申请记录
                password_request = PasswordRequest(
                    file_id=file_id,
                    user_id=user_id,
                    status='pending',  # 待审批状态
                    request_reason='用户申请解压密码',
                    created_at=datetime.now()
                )
                session.add(password_request)
                session.commit()

                self.logger.info(f"记录密码申请成功: 文件ID={file_id}, 用户ID={user_id}")
                return True

        except Exception as e:
            self.logger.error(f"记录密码申请失败: {e}")
            return False
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        try:
            current_time = datetime.now()
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if (current_time - file_time).total_seconds() > max_age_hours * 3600:
                        os.remove(file_path)
                        self.logger.info(f"清理临时文件: {filename}")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")

    def get_user_download_records(self, user_id: int = None, page: int = 1, limit: int = 50,
                                filters: dict = None, include_batch_info: bool = True) -> Dict[str, Any]:
        """获取用户下载记录"""
        try:
            with self.db_manager.get_session() as session:
                from models.download_batch import DownloadBatch
                offset = (page - 1) * limit

                # 构建基础查询
                if include_batch_info:
                    query = session.query(DownloadRecord).outerjoin(DownloadBatch, DownloadRecord.batch_id == DownloadBatch.id)
                else:
                    query = session.query(DownloadRecord)

                # 用户筛选 - 只返回指定用户的记录
                if user_id is not None:
                    query = query.filter(DownloadRecord.user_id == user_id)

                # 应用额外筛选条件
                if filters:
                    if filters.get('download_type'):
                        query = query.filter(DownloadRecord.download_type == filters['download_type'])
                    
                    if filters.get('is_encrypted') is not None:
                        query = query.filter(DownloadRecord.is_encrypted == filters['is_encrypted'])
                    
                    if filters.get('download_source'):
                        query = query.filter(DownloadRecord.download_source == filters['download_source'])
                    
                    if filters.get('date_from'):
                        query = query.filter(DownloadRecord.downloaded_at >= filters['date_from'])
                    
                    if filters.get('date_to'):
                        query = query.filter(DownloadRecord.downloaded_at <= filters['date_to'])
                    
                    if filters.get('min_size'):
                        query = query.filter(DownloadRecord.file_size >= filters['min_size'])
                    
                    if filters.get('max_size'):
                        query = query.filter(DownloadRecord.file_size <= filters['max_size'])

                # 排序和分页
                download_records = query.order_by(DownloadRecord.downloaded_at.desc())\
                    .offset(offset).limit(limit).all()
                
                # 查询总数
                total = query.count()

                # 格式化记录
                formatted_records = []
                for record in download_records:
                    # 获取对应的统计信息（仅对文件下载）
                    stats = None
                    if record.file_id:
                        stats = session.query(DownloadStatistics).filter_by(file_id=record.file_id).first()

                    # 获取批次信息
                    batch_info = None
                    if record.batch_id and include_batch_info:
                        batch = session.query(DownloadBatch).filter_by(id=record.batch_id).first()
                        if batch:
                            batch_info = {
                                'batch_id': batch.batch_id,
                                'batch_type': batch.batch_type,
                                'download_name': batch.download_name,
                                'total_files': batch.total_files,
                                'total_size': batch.total_size,
                                'status': batch.status,
                                'created_at': batch.created_at.isoformat() if batch.created_at else None,
                                'completed_at': batch.completed_at.isoformat() if batch.completed_at else None
                            }

                    # 获取显示名称
                    display_name = record.zip_filename
                    if record.download_type == 'folder' and record.folder_id:
                        # 文件夹下载，获取文件夹名称
                        folder = session.query(SharedFolder).filter_by(id=record.folder_id).first()
                        if folder:
                            display_name = f"文件夹: {folder.name}"
                    elif record.file_id:
                        # 文件下载，获取文件名
                        file_record = session.query(SharedFile).filter_by(id=record.file_id).first()
                        if file_record:
                            display_name = file_record.filename

                    formatted_record = {
                        'id': record.id,
                        'file_id': record.file_id,
                        'folder_id': record.folder_id,
                        'batch_id': record.batch_id,
                        'session_id': record.session_id,
                        'filename': display_name or record.zip_filename,
                        'file_size': record.file_size,
                        'download_time': record.downloaded_at.isoformat() if record.downloaded_at else record.created_at.isoformat(),
                        'download_type': record.download_type,
                        'download_source': record.download_source,
                        'ip_address': record.ip_address,
                        'user_agent': record.user_agent,
                        'is_encrypted': bool(record.is_encrypted),
                        'has_password': bool(record.password),
                        'download_count': stats.total_downloads if stats else 0,
                        'encrypted_download_count': stats.encrypted_downloads if stats else 0,
                        'first_download_time': stats.first_download.isoformat() if stats and stats.first_download else None,
                        'last_download_time': stats.last_download.isoformat() if stats and stats.last_download else None,
                        'zip_filename': record.zip_filename,
                        'download_status': record.download_status,
                        'batch_info': batch_info
                    }
                    formatted_records.append(formatted_record)

                return {
                    "success": True,
                    "records": formatted_records,
                    "total": total,
                    "page": page,
                    "limit": limit
                }

        except Exception as e:
            self.logger.error(f"获取用户下载记录失败: {e}")
            return {"success": False, "error": str(e)}

    def get_all_download_records(self, page: int = 1, limit: int = 50,
                                filters: dict = None, include_user_info: bool = True) -> Dict[str, Any]:
        """获取所有下载记录（管理员用）"""
        try:
            with self.db_manager.get_session() as session:
                from models.download_batch import DownloadBatch
                from models.user import User
                offset = (page - 1) * limit

                # 构建基础查询
                if include_user_info:
                    query = session.query(DownloadRecord, User.username).outerjoin(
                        User, DownloadRecord.user_id == User.id
                    ).outerjoin(DownloadBatch, DownloadRecord.batch_id == DownloadBatch.id)
                else:
                    query = session.query(DownloadRecord).outerjoin(
                        DownloadBatch, DownloadRecord.batch_id == DownloadBatch.id
                    )

                # 应用筛选条件
                if filters:
                    if filters.get('user_id'):
                        query = query.filter(DownloadRecord.user_id == filters['user_id'])

                    if filters.get('download_type'):
                        query = query.filter(DownloadRecord.download_type == filters['download_type'])

                    if filters.get('is_encrypted') is not None:
                        query = query.filter(DownloadRecord.is_encrypted == filters['is_encrypted'])

                    if filters.get('date_from'):
                        try:
                            from datetime import datetime
                            date_from = datetime.strptime(filters['date_from'], '%Y-%m-%d')
                            query = query.filter(DownloadRecord.downloaded_at >= date_from)
                        except ValueError:
                            pass

                    if filters.get('date_to'):
                        try:
                            from datetime import datetime
                            date_to = datetime.strptime(filters['date_to'], '%Y-%m-%d')
                            # 添加一天以包含整个日期
                            date_to = date_to.replace(hour=23, minute=59, second=59)
                            query = query.filter(DownloadRecord.downloaded_at <= date_to)
                        except ValueError:
                            pass

                # 获取总数
                if include_user_info:
                    total_query = session.query(DownloadRecord).outerjoin(
                        User, DownloadRecord.user_id == User.id
                    )
                    # 应用相同的筛选条件
                    if filters:
                        if filters.get('user_id'):
                            total_query = total_query.filter(DownloadRecord.user_id == filters['user_id'])
                        if filters.get('download_type'):
                            total_query = total_query.filter(DownloadRecord.download_type == filters['download_type'])
                        if filters.get('is_encrypted') is not None:
                            total_query = total_query.filter(DownloadRecord.is_encrypted == filters['is_encrypted'])
                        if filters.get('date_from'):
                            try:
                                from datetime import datetime
                                date_from = datetime.strptime(filters['date_from'], '%Y-%m-%d')
                                total_query = total_query.filter(DownloadRecord.downloaded_at >= date_from)
                            except ValueError:
                                pass
                        if filters.get('date_to'):
                            try:
                                from datetime import datetime
                                date_to = datetime.strptime(filters['date_to'], '%Y-%m-%d')
                                date_to = date_to.replace(hour=23, minute=59, second=59)
                                total_query = total_query.filter(DownloadRecord.downloaded_at <= date_to)
                            except ValueError:
                                pass

                    total = total_query.count()
                else:
                    total = query.count()

                # 排序和分页
                query = query.order_by(DownloadRecord.downloaded_at.desc())
                results = query.offset(offset).limit(limit).all()

                # 格式化结果
                formatted_records = []
                for result in results:
                    if include_user_info:
                        record, username = result
                    else:
                        record = result
                        username = None

                    # 获取文件信息
                    file_info = self._get_file_info(record.file_id) if record.file_id else {}

                    formatted_record = {
                        'id': record.id,
                        'user_id': record.user_id,
                        'username': username or 'Unknown',
                        'file_id': record.file_id,
                        'folder_id': record.folder_id,
                        'filename': file_info.get('filename', record.zip_filename or 'Unknown'),
                        'file_size': record.file_size,
                        'download_type': record.download_type,
                        'is_encrypted': record.is_encrypted,
                        'download_time': record.downloaded_at.isoformat() if record.downloaded_at else None,
                        'created_at': record.created_at.isoformat() if record.created_at else None,
                        'ip_address': record.ip_address,
                        'user_agent': record.user_agent,
                        'download_source': record.download_source,
                        'download_status': record.download_status,
                        'zip_path': record.zip_path,
                        'zip_filename': record.zip_filename,
                        'session_id': record.session_id,
                        'batch_id': record.batch_id
                    }
                    formatted_records.append(formatted_record)

                return {
                    "success": True,
                    "records": formatted_records,
                    "total": total,
                    "page": page,
                    "limit": limit
                }

        except Exception as e:
            self.logger.error(f"获取所有下载记录失败: {e}")
            return {"success": False, "error": str(e)}

    def _get_file_info(self, file_id: int) -> Dict[str, Any]:
        """获取文件信息"""
        try:
            if not file_id:
                return {}

            with self.db_manager.get_session() as session:
                file_record = session.query(SharedFile).filter_by(id=file_id).first()
                if file_record:
                    return {
                        'filename': file_record.filename,
                        'file_path': file_record.file_path,
                        'file_size': file_record.file_size,
                        'file_type': file_record.file_type
                    }
                return {}
        except Exception as e:
            self.logger.warning(f"获取文件信息失败 (file_id: {file_id}): {e}")
            return {}
    
    def get_user_download_statistics(self, user_id: int = None, date_range: int = 30) -> Dict[str, Any]:
        """获取用户下载统计分析"""
        try:
            from models.user_download_activity import UserDownloadActivity, UserDownloadSummary
            from datetime import date, timedelta
            
            with self.db_manager.get_session() as session:
                # 获取日期范围
                end_date = date.today()
                start_date = end_date - timedelta(days=date_range)
                
                # 查询用户活动记录
                activities = session.query(UserDownloadActivity)\
                    .filter(UserDownloadActivity.user_id == user_id)\
                    .filter(UserDownloadActivity.activity_date >= start_date)\
                    .filter(UserDownloadActivity.activity_date <= end_date)\
                    .order_by(UserDownloadActivity.activity_date.asc()).all()
                
                # 获取用户汇总统计
                summary = session.query(UserDownloadSummary)\
                    .filter_by(user_id=user_id).first()
                
                # 构建统计结果
                stats = {
                    'period': {
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat(),
                        'days': date_range
                    },
                    'daily_activities': [activity.to_dict() for activity in activities],
                    'summary': summary.to_dict() if summary else None,
                    'trends': self._calculate_download_trends(activities),
                    'insights': self._generate_download_insights(activities, summary)
                }
                
                return {"success": True, "statistics": stats}
                
        except Exception as e:
            self.logger.error(f"获取用户下载统计失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_download_analytics(self, user_id: int = None) -> Dict[str, Any]:
        """获取下载分析数据"""
        try:
            with self.db_manager.get_session() as session:
                from models.download_batch import DownloadBatch
                from sqlalchemy import func
                from datetime import datetime, timedelta
                
                # 基础查询条件
                batch_query = session.query(DownloadBatch)
                record_query = session.query(DownloadRecord)
                
                if user_id is not None:
                    batch_query = batch_query.filter(DownloadBatch.user_id == user_id)
                    record_query = record_query.filter(DownloadRecord.user_id == user_id)
                
                # 最近30天的数据
                thirty_days_ago = datetime.now() - timedelta(days=30)
                
                # 批次统计
                total_batches = batch_query.count()
                recent_batches = batch_query.filter(DownloadBatch.created_at >= thirty_days_ago).count()
                
                # 下载类型分布
                type_distribution = session.query(
                    DownloadBatch.batch_type, 
                    func.count(DownloadBatch.id).label('count')
                ).group_by(DownloadBatch.batch_type).all()
                
                # 下载状态分布
                status_distribution = session.query(
                    DownloadBatch.status,
                    func.count(DownloadBatch.id).label('count')
                ).group_by(DownloadBatch.status).all()
                
                # 下载来源分布
                source_distribution = session.query(
                    DownloadBatch.download_source,
                    func.count(DownloadBatch.id).label('count')
                ).group_by(DownloadBatch.download_source).all()
                
                # 文件大小分析
                size_stats = session.query(
                    func.avg(DownloadBatch.total_size).label('avg_size'),
                    func.max(DownloadBatch.total_size).label('max_size'),
                    func.min(DownloadBatch.total_size).label('min_size'),
                    func.sum(DownloadBatch.total_size).label('total_size')
                ).first()
                
                analytics = {
                    'overview': {
                        'total_batches': total_batches,
                        'recent_batches': recent_batches,
                        'avg_size': float(size_stats.avg_size or 0),
                        'max_size': int(size_stats.max_size or 0),
                        'min_size': int(size_stats.min_size or 0),
                        'total_size': int(size_stats.total_size or 0)
                    },
                    'distributions': {
                        'by_type': [{'type': t, 'count': c} for t, c in type_distribution],
                        'by_status': [{'status': s, 'count': c} for s, c in status_distribution],
                        'by_source': [{'source': s, 'count': c} for s, c in source_distribution]
                    }
                }
                
                return {"success": True, "analytics": analytics}
                
        except Exception as e:
            self.logger.error(f"获取下载分析失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _calculate_download_trends(self, activities: list) -> Dict[str, Any]:
        """计算下载趋势"""
        if not activities:
            return {}
        
        trends = {
            'download_trend': [],
            'size_trend': [],
            'type_trend': {
                'single': [],
                'batch': [],
                'folder': []
            }
        }
        
        for activity in activities:
            date_str = activity.activity_date.isoformat()
            trends['download_trend'].append({
                'date': date_str,
                'downloads': activity.total_downloads
            })
            trends['size_trend'].append({
                'date': date_str,
                'size': activity.total_size
            })
            trends['type_trend']['single'].append({
                'date': date_str,
                'count': activity.single_downloads
            })
            trends['type_trend']['batch'].append({
                'date': date_str,
                'count': activity.batch_downloads
            })
            trends['type_trend']['folder'].append({
                'date': date_str,
                'count': activity.folder_downloads
            })
        
        return trends
    
    def _generate_download_insights(self, activities: list, summary) -> List[str]:
        """生成下载洞察建议"""
        insights = []
        
        if not activities:
            insights.append("暂无最近下载活动数据")
            return insights
        
        # 分析下载频率
        total_downloads = sum(a.total_downloads for a in activities)
        avg_daily = total_downloads / len(activities) if activities else 0
        
        if avg_daily > 10:
            insights.append("您是一位活跃的下载用户，平均每日下载超过10次")
        elif avg_daily > 5:
            insights.append("您有较为规律的下载习惯")
        else:
            insights.append("您的下载频率较低")
        
        # 分析文件类型偏好
        if summary and summary.favorite_file_types:
            top_type = max(summary.favorite_file_types.items(), key=lambda x: x[1])
            insights.append(f"您最常下载的文件类型是 {top_type[0]}，共 {top_type[1]} 次")
        
        # 分析加密使用
        encrypted_ratio = sum(a.encrypted_downloads for a in activities) / max(total_downloads, 1)
        if encrypted_ratio > 0.5:
            insights.append("您经常下载加密文件，安全意识较高")
        elif encrypted_ratio > 0.2:
            insights.append("您偶尔会下载加密文件")
        
        return insights

    def get_user_password_requests(self, user_id: int, page: int = 1, limit: int = 50) -> Dict[str, Any]:
        """获取用户密码申请记录"""
        try:
            with self.db_manager.get_session() as session:
                offset = (page - 1) * limit

                # 查询密码申请记录
                password_requests = session.query(PasswordRequest)\
                    .filter(PasswordRequest.user_id == user_id)\
                    .order_by(PasswordRequest.created_at.desc())\
                    .offset(offset).limit(limit).all()

                # 查询总数
                total = session.query(PasswordRequest).filter_by(user_id=user_id).count()

                # 格式化记录
                formatted_requests = []
                for request in password_requests:
                    # 获取文件信息
                    file_record = session.query(SharedFile).filter_by(id=request.file_id).first()
                    filename = file_record.filename if file_record else f"文件ID: {request.file_id}"

                    formatted_request = {
                        'id': request.id,
                        'file_id': request.file_id,
                        'filename': filename,
                        'request_reason': request.request_reason,
                        'status': request.status,
                        'password_provided': request.password_provided if request.status == 'approved' else None,
                        'password_expires_at': request.password_expires_at.isoformat() if request.password_expires_at else None,
                        'created_at': request.created_at.isoformat() if request.created_at else None,
                        'approved_at': request.approved_at.isoformat() if request.approved_at else None,
                        'approval_reason': request.approval_reason,
                        'is_expired': request.password_expires_at < datetime.now() if request.password_expires_at else False
                    }
                    formatted_requests.append(formatted_request)

                return {
                    "success": True,
                    "requests": formatted_requests,
                    "total": total,
                    "page": page,
                    "limit": limit
                }

        except Exception as e:
            self.logger.error(f"获取用户密码申请记录失败: {e}")
            return {"success": False, "error": str(e)}


