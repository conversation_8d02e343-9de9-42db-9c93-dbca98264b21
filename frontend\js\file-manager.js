/**
 * 文件管理器核心模块
 * 处理文件列表、文件操作等核心功能
 */

class FileManager {
    constructor() {
        this.files = [];
        this.folders = [];
        this.currentFolder = null;
        this.selectedFiles = new Set();
        this.currentViewMode = 'grid';
        this.currentLayoutMode = 'grid';
        this.currentSortField = 'name';
        this.currentSortOrder = 'asc';
        this.searchQuery = '';
        this.searchMode = false;
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalPages = 1;
        this.isLoading = false;
        this.currentView = 'files';
        
        // 初始化排序配置 - 修复sortBy未定义的问题
        this.sortBy = { 
            field: 'name', 
            order: 'asc' 
        };
        
        // 从存储中加载用户偏好设置
        this.loadUserPreferences();
        
        // 绑定方法
        this.handleFileAction = this.handleFileAction.bind(this);
        this.handleKeyboard = this.handleKeyboard.bind(this);
        
        // 防抖函数
        this.refreshThumbnail = Utils.debounce(this.refreshThumbnail.bind(this), 300);
        
        this.init();
    }

    /**
     * 绑定缩略图事件
     */
    bindThumbnailEvents() {
        // 使用事件代理监听缩略图加载事件
        Utils.event.on(document, 'load', (e) => {
            if (e.target.classList && e.target.classList.contains('thumbnail-image')) {
                this.onThumbnailLoad(e.target);
            }
        }, true);

        Utils.event.on(document, 'error', (e) => {
            if (e.target.classList && e.target.classList.contains('thumbnail-image')) {
                this.onThumbnailError(e.target);
            }
        }, true);
    }

    /**
     * 安全显示Toast消息
     */
    showToast(message, type = 'info') {
        if (typeof Components !== 'undefined' && Components.Toast) {
            Components.Toast[type](message);
        } else {
            CONFIG.log(type, message);
        }
    }

    /**
     * 加载用户偏好设置
     */
    loadUserPreferences() {
        try {
            // 加载视图模式
            this.viewMode = Utils.storage.get(CONFIG.STORAGE_KEYS.VIEW_MODE) || 'large-icons';
            this.layoutMode = Utils.storage.get(CONFIG.STORAGE_KEYS.LAYOUT_MODE) || 'grid';
            
            // 加载排序设置
            const savedSortBy = Utils.storage.get(CONFIG.STORAGE_KEYS.SORT_ORDER);
            if (savedSortBy && savedSortBy.field && savedSortBy.order) {
                this.sortBy = savedSortBy;
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to load user preferences:', error);
            // 使用默认设置
            this.viewMode = 'large-icons';
            this.layoutMode = 'grid';
            this.sortBy = { field: 'name', order: 'asc' };
        }
    }

    /**
     * 初始化文件管理器
     */
    init() {
        this.bindEvents();
        this.bindThumbnailEvents();
        this.loadFiles();
        this.setupViewMode();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 布局切换
        Utils.dom.$$('.layout-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                e.preventDefault();
                const layoutMode = e.target.closest('.layout-btn').dataset.layout;
                if (layoutMode) {
                    this.setLayoutMode(layoutMode);
                }
            });
        });

        // 视图切换
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const viewMode = e.target.closest('.view-btn').dataset.view;
                CONFIG.log('info', `视图按钮点击: ${viewMode}, 当前搜索模式: ${this.isInSearchMode}`);
                if (viewMode) {
                    this.setViewMode(viewMode);
                }
            });
        });
        
        // 排序
        const sortSelect = Utils.dom.$('#sort-select');
        if (sortSelect) {
            Utils.event.on(sortSelect, 'change', (e) => {
                const [field, order] = e.target.value.split('-');
                this.setSortOrder(field, order || 'asc');
            });
        }
        
        // 文件夹导航 - 双击进入文件夹
        Utils.event.on(document, 'dblclick', (e) => {
            const folderItem = e.target.closest('.folder-item');
            if (folderItem) {
                const folderId = folderItem.dataset.fileId;
                this.navigateToFolder(folderId);
            }
        });
        
        // 面包屑导航
        Utils.event.on(document, 'click', (e) => {
            const breadcrumbItem = e.target.closest('.breadcrumb-item');
            if (breadcrumbItem) {
                e.preventDefault();

                // 如果在搜索模式下，清除搜索状态
                if (this.isInSearchMode) {
                    CONFIG.log('info', '面包屑导航：清除搜索模式');
                    this.clearSearchMode();
                }

                const folderId = breadcrumbItem.dataset.folderId;
                this.navigateToFolder(folderId || null);
            }
        });
        
        // 文件选择
        Utils.event.on(document, 'click', (e) => {
            // 如果点击的是操作按钮或其子元素，不处理文件选择
            if (e.target.closest('.action-btn')) {
                return;
            }

            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                this.handleFileClick(fileItem, e);
            }
        });
        
        // 右键菜单
        Utils.event.on(document, 'contextmenu', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                e.preventDefault();
                this.showContextMenu(e.clientX, e.clientY, fileItem);
            }
        });
        
        // 双击文件 - 避免与文件夹双击冲突
        Utils.event.on(document, 'dblclick', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem && !fileItem.classList.contains('folder-item')) {
                this.handleFileDoubleClick(fileItem);
            }
        });

        // 文件操作按钮点击事件
        Utils.event.on(document, 'click', (e) => {
            console.log('=== 全局点击事件触发 ===');
            console.log('点击目标:', e.target);
            console.log('点击目标类名:', e.target.className);
            console.log('点击目标父元素:', e.target.parentElement);

            const actionBtn = e.target.closest('.action-btn');
            console.log('找到的操作按钮:', actionBtn);

            if (actionBtn) {
                console.log('操作按钮详情:', {
                    className: actionBtn.className,
                    dataset: actionBtn.dataset,
                    innerHTML: actionBtn.innerHTML
                });

                e.preventDefault();
                e.stopPropagation();

                const action = actionBtn.dataset.action;
                const fileItem = actionBtn.closest('.file-item');
                const fileId = fileItem?.dataset.fileId;

                console.log('=== 操作按钮点击详情 ===');
                console.log('action:', action);
                console.log('fileId:', fileId);
                console.log('fileItem:', fileItem);

                CONFIG.log('info', `操作按钮点击: action=${action}, fileId=${fileId}, timestamp=${Date.now()}`);

                if (!fileId) {
                    CONFIG.log('error', '未找到文件ID');
                    console.error('未找到文件ID，fileItem:', fileItem);
                    return;
                }

                switch (action) {
                    case 'download':
                        CONFIG.log('info', `执行下载: ${fileId}`);
                        this.downloadFile(fileId);
                        break;
                    case 'preview':
                        console.log('=== 执行预览操作 ===');
                        CONFIG.log('info', `执行预览: ${fileId}`);
                        this.previewFile(fileId);
                        break;

                    case 'open':
                        CONFIG.log('info', `执行打开: ${fileId}`);
                        this.navigateToFolder(fileId);
                        break;
                    case 'download-folder':
                        CONFIG.log('info', `执行文件夹下载: ${fileId}`);

                        // 防止重复点击 - 检查按钮是否已被禁用
                        if (actionBtn.disabled) {
                            CONFIG.log('warn', '文件夹下载按钮已禁用，忽略重复点击');
                            return;
                        }

                        // 临时禁用按钮
                        actionBtn.disabled = true;
                        const originalHTML = actionBtn.innerHTML;
                        actionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                        this.downloadFolder(fileId).finally(() => {
                            // 恢复按钮状态
                            setTimeout(() => {
                                actionBtn.disabled = false;
                                actionBtn.innerHTML = originalHTML;
                            }, 1000); // 1秒后恢复
                        });
                        break;
                    default:
                        CONFIG.log('warn', `未知操作: ${action}`);
                        console.warn('未知操作:', action);
                }
            } else {
                // 如果不是操作按钮，检查是否是图标点击
                if (e.target.tagName === 'I' && e.target.classList.contains('fa-eye')) {
                    console.log('=== 直接点击了眼睛图标 ===');
                    console.log('眼睛图标父元素:', e.target.parentElement);
                    const btn = e.target.parentElement;
                    if (btn && btn.classList.contains('action-btn')) {
                        console.log('触发父按钮点击');
                        btn.click();
                    }
                }
            }
        });

        // 侧边栏菜单点击
        Utils.event.on(document, 'click', (e) => {
            const menuLink = e.target.closest('.menu-item a[data-view]');
            if (menuLink) {
                e.preventDefault();
                const view = menuLink.dataset.view;
                CONFIG.log('info', `侧边栏菜单点击: ${view}`);

                switch (view) {

                    case 'home':
                        this.navigateToFolder(null);
                        break;
                    case 'downloads':
                        // 切换到下载记录视图
                        this.switchView('downloads');
                        break;
                    default:
                        CONFIG.log('warn', `未知的菜单视图: ${view}`);
                }
            }
        });

        // 文件复选框事件
        Utils.event.on(document, 'change', (e) => {
            if (e.target.classList.contains('file-checkbox')) {
                e.stopPropagation();
                const fileId = e.target.dataset.fileId;
                this.handleFileCheckboxChange(fileId, e.target.checked);
            }
        });

        // 键盘快捷键
        Utils.event.on(document, 'keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    

    

    
    /**
     * 加载文件列表
     */
    async loadFiles(folderId = null) {
        try {
            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.show(folderId ? '正在加载文件...' : '正在加载文件夹...');
            }

            CONFIG.log('info', `Loading files for folder: ${folderId}`);

            // 检查认证状态
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                this.showToast('请先登录', 'error');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                return;
            }

            this.currentFolderId = folderId;
            this.isInFolder = !!folderId;
            this.isShowingFavorites = false;

            let files = [];
            let response = null;

            if (this.isInFolder) {
                // 在文件夹内：获取文件夹中的文件
                response = await FileAPI.getFiles(folderId);
                CONFIG.log('info', 'Files API Response:', response);

                // 处理不同的响应格式
                if (response && response.files) {
                    files = response.files;
                } else if (Array.isArray(response)) {
                    files = response;
                } else if (response && response.data && Array.isArray(response.data)) {
                    files = response.data;
                }

                // 在文件夹内：显示所有图片文件
                files = this.filterAllowedFiles(files);
            } else {
                // 在首页：获取共享文件夹列表
                const folders = await FolderAPI.getSharedFolders();
                CONFIG.log('info', 'Folders API Response:', folders);

                // 将文件夹转换为文件格式，添加type属性
                files = (folders || []).map(folder => ({
                    id: folder.id,
                    name: folder.name,
                    type: 'folder',
                    size: folder.statistics?.total_size || 0,
                    modified_at: folder.updated_at || folder.created_at,
                    file_count: folder.statistics?.file_count || folder.file_count || 0,
                    path: folder.path
                }));
            }

            CONFIG.log('info', `Processed ${files.length} items`);

            this.files = files;
            this.currentFolder = response?.folder || null;

            this.renderFiles();
            this.updateBreadcrumb();

            // 显示加载结果（只在首次加载或文件夹变化时显示）
            if (!this._lastLoadedFolderId || this._lastLoadedFolderId !== folderId) {
                // 只在真正需要时显示Toast消息
                if (files.length === 0) {
                    // 空状态消息已经在renderEmptyState中处理，这里不再显示Toast
                    CONFIG.log('info', this.isInFolder ? '当前文件夹没有图片文件' : '没有共享文件夹');
                } else {
                    // 只在成功加载文件时显示成功消息
                    if (this.isInFolder) {
                        this.showToast(`加载了 ${files.length} 个文件`, 'success');
                    } else {
                        this.showToast(`找到 ${files.length} 个文件夹`, 'success');
                    }
                }
                this._lastLoadedFolderId = folderId;
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to load files:', error);

            // 更详细的错误信息
            let errorMessage = '加载失败';
            if (error.userMessage) {
                errorMessage = error.userMessage;
            } else if (error.message) {
                if (error.message.includes('401')) {
                    errorMessage = '请先登录';
                } else if (error.message.includes('403')) {
                    errorMessage = '没有访问权限';
                } else if (error.message.includes('404')) {
                    errorMessage = '文件夹不存在';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '无法连接到服务器';
                }
            }

            this.showToast(errorMessage, 'error');

            // 如果是认证错误，跳转到登录页
            if (error.message && (error.message.includes('401') || error.message.includes('请先登录'))) {
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        } finally {
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }
        }
    }



    /**
     * 生成缩略图HTML
     */
    generateThumbnailHTML(file, icon) {
        // 使用API缩略图
        const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');
        const uniqueId = `thumb-${file.id}-${Date.now()}`;

        return `
            <div class="thumbnail-container" id="${uniqueId}">
                <div class="thumbnail-loading">
                    <div class="loading-spinner-small"></div>
                </div>
                <img src="${thumbnailUrl}"
                     alt="${file.name}"
                     class="thumbnail-image"
                     style="display:none;"
                     data-file-id="${file.id}"
                     data-file-name="${file.name}"
                     onload="window.safeThumbnailLoad(this)"
                     onerror="window.safeThumbnailError(this)">
                <div class="thumbnail-fallback" style="display:none;">
                    <i class="${icon}"></i>
                </div>
            </div>
        `;
    }

    /**
     * 缩略图加载成功处理
     */
    onThumbnailLoad(img) {
        const container = img.parentElement;
        const loading = container.querySelector('.thumbnail-loading');
        if (loading) loading.style.display = 'none';
        img.style.display = 'block';

        CONFIG.log('info', `缩略图加载成功: ${img.dataset.fileName}`);
    }

    /**
     * 缩略图加载失败处理
     */
    onThumbnailError(img) {
        const container = img.parentElement;
        const loading = container.querySelector('.thumbnail-loading');
        const fallback = container.querySelector('.thumbnail-fallback');

        if (loading) loading.style.display = 'none';
        if (fallback) fallback.style.display = 'flex';

        CONFIG.log('warn', `缩略图加载失败: ${img.dataset.fileName}, URL: ${img.src}`);

        // 尝试重新加载（添加时间戳避免缓存）
        setTimeout(() => {
            const newUrl = img.src.includes('&retry=') ?
                img.src : `${img.src}&retry=${Date.now()}`;

            if (!img.dataset.retried) {
                img.dataset.retried = 'true';
                img.src = newUrl;
                CONFIG.log('info', `重试缩略图加载: ${img.dataset.fileName}`);
            }
        }, 1000);
    }

    /**
     * 预加载缩略图
     */
    preloadThumbnails(files) {
        // 只预加载图片文件的缩略图
        const imageFiles = files.filter(file =>
            Utils.isImageFile(file.name) && file.type !== 'folder'
        );

        // 限制同时预加载的数量，避免过多请求
        const maxConcurrent = 6;
        let currentIndex = 0;

        const loadNext = () => {
            if (currentIndex >= imageFiles.length) return;

            const file = imageFiles[currentIndex++];
            const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');

            // 创建隐藏的图片元素来预加载
            const img = new Image();
            img.onload = () => {
                CONFIG.log('info', `缩略图预加载成功: ${file.name}`);
                loadNext();
            };
            img.onerror = () => {
                CONFIG.log('warn', `缩略图预加载失败: ${file.name}`);
                loadNext();
            };
            img.src = thumbnailUrl;
        };

        // 启动并发预加载
        for (let i = 0; i < Math.min(maxConcurrent, imageFiles.length); i++) {
            loadNext();
        }
    }

    /**
     * 过滤允许的文件 - 只显示图片格式
     */
    filterAllowedFiles(files) {
        return files.filter(file => {
            // 文件夹总是显示
            if (file.type === 'folder') {
                return true;
            }

            // 只显示允许的图片格式
            return CONFIG.FILES.isAllowedFile(file.name);
        });
    }
    
    /**
     * 渲染文件列表
     */
    renderFiles() {
        const sortedFiles = this.sortFiles(this.files);

        // 根据视图模式渲染
        switch (this.viewMode) {
            case 'extra-large-icons':
            case 'large-icons':
            case 'medium-icons':
            case 'small-icons':
                this.renderIconView(sortedFiles);
                break;
            default:
                this.renderIconView(sortedFiles);
        }

        // 更新视图模式按钮状态
        this.updateViewModeButtons();

        // 确保批量操作按钮存在（仅在文件夹内显示）
        if (this.isInFolder) {
            this.ensureBatchActionButtons();
        }
    }
    
    /**
     * 渲染图标视图
     */
    renderIconView(files) {
        const container = Utils.dom.$('#file-grid');
        if (!container) return;

        container.innerHTML = '';
        Utils.dom.show(container);
        Utils.dom.hide(Utils.dom.$('#file-list'));

        // 设置容器的CSS类以控制图标大小和布局
        let containerClasses = ['file-grid'];

        // 添加布局模式类
        if (this.layoutMode === 'horizontal' && !this.isInFolder) {
            // 在首页且为横向布局时，使用横向布局
            containerClasses.push('horizontal-layout');
        } else if (this.isInFolder) {
            // 在文件夹内，使用指定的视图模式
            containerClasses.push(this.viewMode);
        }
        // 在首页且为网格布局时，使用默认样式（不添加额外类）

        container.className = containerClasses.join(' ');

        // 如果没有文件，显示空状态
        if (files.length === 0) {
            this.renderEmptyState(container);
            return;
        }

        files.forEach(file => {
            const fileElement = this.createFileIconItem(file);
            container.appendChild(fileElement);
        });

        // 预加载缩略图
        this.preloadThumbnails(files);
    }

    /**
     * 渲染空状态
     */
    renderEmptyState(container) {
        let emptyStateHTML = '';

        if (this.isInSearchMode) {
            emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>未找到匹配的文件</h3>
                    <p>请尝试其他搜索关键词</p>
                </div>
            `;
        } else if (this.isInFolder) {
            emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>文件夹为空</h3>
                    <p>当前文件夹没有图片文件</p>
                </div>
            `;
        } else {
            emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder"></i>
                    <h3>暂无共享文件夹</h3>
                    <p>管理员还没有设置共享文件夹</p>
                </div>
            `;
        }

        container.innerHTML = emptyStateHTML;
    }
    

    
    /**
     * 创建图标文件项
     */
    createFileIconItem(file) {
        const isImage = Utils.isImageFile(file.name);
        const isFolder = file.type === 'folder';
        const icon = CONFIG.FILES.getFileIcon(file.name, isFolder);


        // 对所有图片文件显示缩略图（不限制在文件夹内）
        const showThumbnail = isImage && !isFolder;



        const element = Utils.dom.create('div', {
            className: `file-item ${isFolder ? 'folder-item' : 'file-item'}`,
            'data-file-id': file.id,
            'data-file-type': file.type,
            innerHTML: `
                ${!isFolder ? `
                    <div class="file-checkbox-wrapper">
                        <input type="checkbox" class="file-checkbox" data-file-id="${file.id}">
                    </div>
                ` : ''}
                <div class="file-icon">
                    ${showThumbnail ?
                        this.generateThumbnailHTML(file, icon) :
                        `<i class="${icon}"></i>`
                    }
                </div>
                <div class="file-name" title="${file.name}">${file.name}</div>
                ${!isFolder && this.viewMode !== 'small-icons' ? `
                    <div class="file-meta">
                        <span class="file-size">${Utils.formatFileSize(file.size)}</span>
                    </div>
                ` : ''}
                <div class="file-actions">
                    ${isFolder ? `
                        <button class="action-btn" data-action="open" title="打开">
                            <i class="fas fa-folder-open"></i>
                        </button>
                        <button class="action-btn" data-action="download-folder" title="下载文件夹">
                            <i class="fas fa-download"></i>
                        </button>
                    ` : `
                        <button class="action-btn" data-action="download" title="下载">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="action-btn" data-action="preview" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>

                    `}
                </div>
            `
        });

        return element;
    }
    

    
    /**
     * 获取文件类型文本 - 专门针对图片文件
     */
    getFileTypeText(filename) {
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));

        // 根据扩展名返回具体的图片类型
        const imageTypeMap = {
            '.jpg': 'JPEG图片',
            '.jpeg': 'JPEG图片',
            '.png': 'PNG图片',
            '.psd': 'Photoshop文档',
            '.tif': 'TIFF图片',
            '.tiff': 'TIFF图片',
            '.ai': 'Illustrator文档',
            '.eps': 'EPS矢量图',
            '.gif': 'GIF动图',
            '.bmp': 'BMP图片',
            '.webp': 'WebP图片',
            '.svg': 'SVG矢量图'
        };

        return imageTypeMap[ext] || '图片文件';
    }
    
    /**
     * 排序文件
     */
    sortFiles(files) {
        return [...files].sort((a, b) => {
            // 文件夹优先
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            
            let aValue, bValue;
            
            switch (this.sortBy.field) {
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                case 'size':
                    aValue = a.size || 0;
                    bValue = b.size || 0;
                    break;
                case 'date':
                    aValue = new Date(a.modified_at);
                    bValue = new Date(b.modified_at);
                    break;
                case 'type':
                    aValue = this.getFileTypeText(a.name);
                    bValue = this.getFileTypeText(b.name);
                    break;
                default:
                    return 0;
            }
            
            if (aValue < bValue) return this.sortBy.order === 'asc' ? -1 : 1;
            if (aValue > bValue) return this.sortBy.order === 'asc' ? 1 : -1;
            return 0;
        });
    }
    
    /**
     * 设置视图模式
     */
    setViewMode(mode) {
        CONFIG.log('info', `设置视图模式: ${mode}, 搜索模式: ${this.isInSearchMode}, 搜索结果数量: ${this.searchResults.length}`);

        this.viewMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.VIEW_MODE, mode);

        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults.length > 0) {
            CONFIG.log('info', '保持搜索结果，不重新加载文件');
            this.files = this.searchResults;
        } else {
            CONFIG.log('info', '非搜索模式或无搜索结果，使用当前文件列表');
        }

        this.renderFiles();
    }

    /**
     * 设置布局模式
     */
    setLayoutMode(mode) {
        this.layoutMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.LAYOUT_MODE, mode);

        // 更新按钮状态
        Utils.dom.$$('.layout-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.layout === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults.length > 0) {
            this.files = this.searchResults;
        }

        this.renderFiles();
    }
    
    /**
     * 设置排序方式
     */
    setSortOrder(field, order = 'asc') {
        this.sortBy = { field, order };
        Utils.storage.set(CONFIG.STORAGE_KEYS.SORT_ORDER, this.sortBy);

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults.length > 0) {
            this.files = this.searchResults;
        }

        this.renderFiles();
    }
    
    /**
     * 设置视图模式
     */
    setupViewMode() {
        this.setViewMode(this.viewMode);
        this.setLayoutMode(this.layoutMode);
    }
    
    /**
     * 导航到文件夹
     */
    navigateToFolder(folderId) {
        // 重置加载标志，允许显示新的Toast消息
        this._lastLoadedFolderId = null;

        // 如果在搜索模式下，清除搜索状态
        if (this.isInSearchMode) {
            this.clearSearchMode();
        }



        this.selectedFiles.clear();
        this.loadFiles(folderId);
    }

    /**
     * 清除搜索模式
     */
    clearSearchMode() {
        this.isInSearchMode = false;
        this.searchResults = [];

        // 同时清除搜索管理器的状态
        if (window.searchManager) {
            window.searchManager.isInSearchMode = false;
            window.searchManager.searchResults = [];
        }
    }

    /**
     * 更新视图模式按钮状态
     */
    updateViewModeButtons() {
        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === this.viewMode) {
                Utils.dom.addClass(btn, 'active');
            }
        });
    }
    
    /**
     * 更新面包屑导航
     */
    updateBreadcrumb(items = null) {
        const container = Utils.dom.$('.breadcrumb-nav');
        if (!container) return;

        // 如果传入了自定义items，使用新的格式
        if (items && Array.isArray(items)) {
            let html = '';
            items.forEach((item, index) => {
                if (index === items.length - 1) {
                    html += `
                        <span class="breadcrumb-item current">
                            <i class="${item.icon}"></i>
                            ${item.name}
                        </span>
                    `;
                } else {
                    html += `
                        <a href="#" class="breadcrumb-item" onclick="fileManager.switchView('${item.view || 'home'}')">
                            <i class="${item.icon}"></i>
                            ${item.name}
                        </a>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    `;
                }
            });
            container.innerHTML = html;
            return;
        }

        // 默认的文件浏览面包屑
        container.innerHTML = `
            <a href="#" class="breadcrumb-item" data-folder-id="">
                <i class="fas fa-home"></i>
                首页
            </a>
        `;

        if (this.isInFolder && this.currentFolder) {
            container.innerHTML += ' <span class="breadcrumb-separator">/</span> ';
            const folderLink = Utils.dom.create('a', {
                className: 'breadcrumb-item active',
                'data-folder-id': this.currentFolder.id,
                textContent: this.currentFolder.name
            });
            container.appendChild(folderLink);
        }
    }
    
    /**
     * 处理文件点击
     */
    handleFileClick(fileItem, event) {
        const fileId = fileItem.dataset.fileId;
        
        if (event.ctrlKey || event.metaKey) {
            // 多选
            this.toggleFileSelection(fileId, fileItem);
        } else {
            // 单选
            this.clearSelection();
            this.selectFile(fileId, fileItem);
        }
    }
    
    /**
     * 处理文件双击
     */
    handleFileDoubleClick(fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        if (fileType === 'folder') {
            this.navigateToFolder(fileId);
        } else {
            this.previewFile(fileId);
        }
    }
    
    /**
     * 选择文件
     */
    selectFile(fileId, fileItem) {
        this.selectedFiles.add(fileId);
        Utils.dom.addClass(fileItem, 'selected');
    }
    
    /**
     * 切换文件选择状态
     */
    toggleFileSelection(fileId, fileItem) {
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            Utils.dom.removeClass(fileItem, 'selected');
        } else {
            this.selectedFiles.add(fileId);
            Utils.dom.addClass(fileItem, 'selected');
        }
    }
    
    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedFiles.clear();
        Utils.dom.$$('.file-item.selected').forEach(item => {
            Utils.dom.removeClass(item, 'selected');
        });
    }
    
    /**
     * 显示右键菜单
     */
    showContextMenu(x, y, fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        const menuItems = [
            {
                icon: 'fas fa-download',
                text: '下载',
                action: 'download',
                handler: () => this.downloadFile(fileId)
            },
            {
                icon: 'fas fa-eye',
                text: '预览',
                action: 'preview',
                handler: () => this.previewFile(fileId)
            },

            {
                icon: 'fas fa-share',
                text: '分享',
                action: 'share',
                handler: () => this.shareFile(fileId)
            },
            { divider: true },
            {
                icon: 'fas fa-info-circle',
                text: '详细信息',
                action: 'info',
                handler: () => this.showFileInfo(fileId)
            }
        ];
        
        if (typeof Components !== 'undefined' && Components.ContextMenu) {
            Components.ContextMenu.show(x, y, menuItems);
        }
    }
    
    /**
     * 处理文件操作
     */
    handleFileAction(action, file) {
        const fileId = file.id;

        switch (action) {
            case 'download':
                this.downloadFile(fileId);
                break;
            case 'preview':
                this.previewFile(fileId);
                break;
            case 'open':
                if (file.type === 'folder') {
                    this.navigateToFolder(fileId);
                }
                break;
            default:
                CONFIG.log('warn', `Unknown file action: ${action}`);
        }
    }

    /**
     * 下载文件
     */
    async downloadFile(fileId) {
        try {
            // 获取正确的认证token
            const authData = localStorage.getItem('fileShareAuth');
            let token = '';
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    token = auth.token || '';
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                }
            }

            // 使用新的单文件下载接口，确保所有下载都是压缩包形式
            const response = await fetch(`${api.getBaseURL()}/download/single/${fileId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 423) {
                // 文件被加密，需要申请密码
                const errorData = await response.json();
                const fileName = errorData.filename || `文件_${fileId}`;
                this.showPasswordRequestDialog(fileId, fileName);
                return;
            }

            if (!response.ok) {
                throw new Error(`下载失败: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                const downloadData = result.data;

                if (downloadData.is_encrypted) {
                    // 文件已加密，显示密码申请对话框
                    this.showPasswordRequestDialog(fileId, downloadData.filename);
                } else {
                    // 文件未加密，直接下载
                    const downloadUrl = `${api.getBaseURL()}${downloadData.download_url}`;
                    Utils.url.downloadFile(downloadUrl, downloadData.filename);
                    this.showToast('文件下载成功', 'success');

                    // 刷新下载记录
                    this.refreshDownloadRecords();
                }
            } else {
                throw new Error(result.error || '下载失败');
            }

        } catch (error) {
            CONFIG.log('error', 'Download failed:', error);
            this.showToast('文件下载失败', 'error');
        }
    }
    
    /**
     * 预览文件
     */
    async previewFile(fileId) {
        try {
            console.log('=== previewFile 开始 ===');
            console.log('fileId:', fileId, 'typeof:', typeof fileId);
            console.log('this.files:', this.files);

            // 尝试多种匹配方式
            let file = this.files.find(f => f.id == fileId); // 使用 == 进行类型转换匹配

            console.log('找到的文件:', file);

            // 调试：显示所有文件的ID和类型
            console.log('所有文件的ID:', this.files.map(f => ({ id: f.id, type: typeof f.id, name: f.name })));

            if (!file) {
                console.error('未找到文件，fileId:', fileId);
                return;
            }

            console.log('文件名:', file.name);
            console.log('Utils.isImageFile 检查结果:', Utils.isImageFile(file.name));

            // 如果是图片文件，使用浮窗预览
            if (Utils.isImageFile(file.name)) {
                console.log('确认是图片文件，调用 showImagePreviewFloat');
                this.showImagePreviewFloat(fileId, file);
                return;
            } else {
                console.log('不是图片文件，使用模态框预览');
            }

            // 非图片文件使用原有的模态框预览
            const previewModal = Utils.dom.$('#preview-modal');
            const previewTitle = Utils.dom.$('#preview-title');
            const previewContainer = Utils.dom.$('#preview-container');

            if (!previewModal || !previewTitle || !previewContainer) return;

            previewTitle.textContent = file.name;
            previewContainer.innerHTML = '<div class="loading-spinner"><div class="spinner"></div></div>';

            if (typeof Components !== 'undefined' && Components.Modal) {
                Components.Modal.show('preview-modal');
            }

            if (Utils.isVideoFile(file.name)) {
                const video = Utils.dom.create('video', {
                    controls: true,
                    src: `${api.getBaseURL()}/files/stream/${fileId}`,
                    style: 'max-width: 100%; max-height: 80vh;'
                });
                previewContainer.innerHTML = '';
                previewContainer.appendChild(video);
            } else {
                previewContainer.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-file"></i>
                        <p>此文件类型不支持预览</p>
                        <button class="btn btn-primary" onclick="fileManager.downloadFile('${fileId}')">
                            <i class="fas fa-download"></i>
                            下载文件
                        </button>
                    </div>
                `;
            }
        } catch (error) {
            CONFIG.log('error', 'Preview failed:', error);
            this.showToast('文件预览失败', 'error');
        }
    }

    /**
     * 显示图片预览浮窗
     */
    async showImagePreviewFloat(fileId, file) {
        try {
            console.log('=== showImagePreviewFloat 开始 ===');
            console.log('文件ID:', fileId);
            console.log('文件信息:', file);
            console.log('typeof Components:', typeof Components);
            console.log('Components:', Components);
            console.log('Components.ImagePreviewFloat:', Components ? Components.ImagePreviewFloat : 'Components未定义');

            // 检查浮窗组件是否可用
            if (typeof Components === 'undefined' || !Components.ImagePreviewFloat) {
                console.warn('ImagePreviewFloat component not available, fallback to modal');
                CONFIG.log('warn', 'ImagePreviewFloat component not available, fallback to modal');
                this.createImagePreview(fileId, file, Utils.dom.$('#preview-container'));
                return;
            }

            console.log('浮窗组件可用，开始获取图片URL');

            // 获取图片URL
            const imageUrl = await this.getImageUrlWithAuth(fileId);
            console.log('图片URL获取成功:', imageUrl);

            // 显示浮窗
            console.log('调用 Components.ImagePreviewFloat.show');
            Components.ImagePreviewFloat.show(imageUrl, file.name, fileId);

            // 更新浮窗中的收藏按钮状态
            setTimeout(() => {
                console.log('更新收藏按钮状态');
                Components.ImagePreviewFloat.updateFavoriteButton();
            }, 100);

            CONFIG.log('info', `图片预览浮窗已显示: ${file.name}`);
            console.log('=== showImagePreviewFloat 完成 ===');

        } catch (error) {
            console.error('showImagePreviewFloat 错误:', error);
            CONFIG.log('error', 'Failed to show image preview float:', error);
            this.showToast('图片预览失败', 'error');
        }
    }

    /**
     * 获取带认证的图片URL
     */
    async getImageUrlWithAuth(fileId) {
        try {
            // 获取认证token
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;

            // 首先尝试预览URL
            const previewUrl = FileAPI.getPreviewURL(fileId);
            const response = await fetch(previewUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (response.ok) {
                const blob = await response.blob();
                return URL.createObjectURL(blob);
            } else {
                // 如果预览失败，尝试缩略图
                const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'large');
                const thumbnailResponse = await fetch(thumbnailUrl, {
                    headers: token ? {
                        'Authorization': `Bearer ${token}`
                    } : {}
                });

                if (thumbnailResponse.ok) {
                    const blob = await thumbnailResponse.blob();
                    return URL.createObjectURL(blob);
                } else {
                    throw new Error('无法获取图片数据');
                }
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to get image URL with auth:', error);
            throw error;
        }
    }

    /**
     * 创建图片预览
     */
    createImagePreview(fileId, file, container) {
        // 创建预览容器
        const previewWrapper = Utils.dom.create('div', {
            className: 'image-preview-wrapper'
        });

        // 创建工具栏
        const toolbar = Utils.dom.create('div', {
            className: 'preview-toolbar',
            innerHTML: `
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="zoom-out" title="缩小">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level">100%</span>
                    <button class="toolbar-btn" data-action="zoom-in" title="放大">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="toolbar-btn" data-action="zoom-fit" title="适应窗口">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button class="toolbar-btn" data-action="zoom-actual" title="实际大小">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="rotate-left" title="向左旋转">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="toolbar-btn" data-action="rotate-right" title="向右旋转">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            `
        });

        // 创建图片容器
        const imageContainer = Utils.dom.create('div', {
            className: 'image-container'
        });

        // 创建图片元素
        const img = Utils.dom.create('img', {
            className: 'preview-image',
            alt: file.name,
            draggable: false
        });

        // 异步加载图片（带认证）
        this.loadImageWithAuth(fileId, img);

        // 预览状态
        const previewState = {
            scale: 1,
            rotation: 0,
            translateX: 0,
            translateY: 0,
            isDragging: false,
            lastX: 0,
            lastY: 0
        };

        // 更新图片变换
        const updateTransform = () => {
            const transform = `translate(${previewState.translateX}px, ${previewState.translateY}px) scale(${previewState.scale}) rotate(${previewState.rotation}deg)`;
            img.style.transform = transform;

            // 更新缩放显示
            const zoomLevel = toolbar.querySelector('.zoom-level');
            if (zoomLevel) {
                zoomLevel.textContent = Math.round(previewState.scale * 100) + '%';
            }
        };

        // 图片加载完成后的处理
        img.onload = () => {
            this.fitImageToContainer(img, imageContainer, previewState, updateTransform);
        };

        // 图片加载错误处理
        img.onerror = () => {
            // 如果预览失败，尝试使用缩略图
            img.src = FileAPI.getThumbnailURL(fileId, 'large');
            img.onerror = () => {
                // 如果缩略图也失败，显示错误信息
                container.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-image"></i>
                        <p>图片预览失败</p>
                        <button class="btn btn-primary" onclick="fileManager.downloadFile('${fileId}')">
                            <i class="fas fa-download"></i>
                            下载文件
                        </button>
                    </div>
                `;
            };
        };

        // 绑定工具栏事件
        this.bindPreviewToolbarEvents(toolbar, img, previewState, updateTransform, fileId);

        // 绑定图片交互事件
        this.bindImageInteractionEvents(img, imageContainer, previewState, updateTransform);

        // 组装预览界面
        imageContainer.appendChild(img);
        previewWrapper.appendChild(toolbar);
        previewWrapper.appendChild(imageContainer);

        container.innerHTML = '';
        container.appendChild(previewWrapper);
    }
    








    /**
     * 带认证的图片加载
     */
    async loadImageWithAuth(fileId, imgElement) {
        try {
            // 获取认证token
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;

            // 首先尝试预览URL
            const previewUrl = FileAPI.getPreviewURL(fileId);
            const response = await fetch(previewUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (response.ok) {
                const blob = await response.blob();
                const imageUrl = URL.createObjectURL(blob);
                imgElement.src = imageUrl;

                // 清理blob URL（在图片加载完成后）
                imgElement.onload = () => {
                    setTimeout(() => {
                        URL.revokeObjectURL(imageUrl);
                    }, 1000);
                };

                return;
            }

            // 如果预览失败，尝试缩略图
            const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'large');
            const thumbnailResponse = await fetch(thumbnailUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (thumbnailResponse.ok) {
                const blob = await thumbnailResponse.blob();
                const imageUrl = URL.createObjectURL(blob);
                imgElement.src = imageUrl;

                // 清理blob URL
                imgElement.onload = () => {
                    setTimeout(() => {
                        URL.revokeObjectURL(imageUrl);
                    }, 1000);
                };

                return;
            }

            // 如果都失败了，触发错误处理
            if (imgElement.onerror) {
                imgElement.onerror();
            }

        } catch (error) {
            CONFIG.log('error', '带认证的图片加载失败:', error);
            if (imgElement.onerror) {
                imgElement.onerror();
            }
        }
    }

    /**
     * 适应图片到容器
     */
    fitImageToContainer(img, container, state, updateTransform) {
        const containerRect = container.getBoundingClientRect();
        const imgNaturalWidth = img.naturalWidth;
        const imgNaturalHeight = img.naturalHeight;

        if (imgNaturalWidth && imgNaturalHeight) {
            const containerRatio = containerRect.width / containerRect.height;
            const imageRatio = imgNaturalWidth / imgNaturalHeight;

            let scale;
            if (imageRatio > containerRatio) {
                // 图片更宽，以宽度为准
                scale = (containerRect.width * 0.9) / imgNaturalWidth;
            } else {
                // 图片更高，以高度为准
                scale = (containerRect.height * 0.9) / imgNaturalHeight;
            }

            state.scale = Math.min(scale, 1); // 不超过原始大小
            state.translateX = 0;
            state.translateY = 0;
            updateTransform();
        }
    }

    /**
     * 绑定预览工具栏事件
     */
    bindPreviewToolbarEvents(toolbar, img, state, updateTransform, fileId) {
        Utils.event.on(toolbar, 'click', (e) => {
            const btn = e.target.closest('.toolbar-btn');
            if (!btn) return;

            const action = btn.dataset.action;

            switch (action) {
                case 'zoom-in':
                    state.scale = Math.min(state.scale * 1.2, 5);
                    updateTransform();
                    break;

                case 'zoom-out':
                    state.scale = Math.max(state.scale / 1.2, 0.1);
                    updateTransform();
                    break;

                case 'zoom-fit':
                    this.fitImageToContainer(img, img.parentElement, state, updateTransform);
                    break;

                case 'zoom-actual':
                    state.scale = 1;
                    state.translateX = 0;
                    state.translateY = 0;
                    updateTransform();
                    break;

                case 'rotate-left':
                    state.rotation -= 90;
                    updateTransform();
                    break;

                case 'rotate-right':
                    state.rotation += 90;
                    updateTransform();
                    break;



                case 'download':
                    this.downloadFile(fileId);
                    break;
            }
        });
    }

    /**
     * 绑定图片交互事件
     */
    bindImageInteractionEvents(img, container, state, updateTransform) {
        // 鼠标拖拽
        Utils.event.on(img, 'mousedown', (e) => {
            e.preventDefault();
            state.isDragging = true;
            state.lastX = e.clientX;
            state.lastY = e.clientY;
            img.style.cursor = 'grabbing';
        });

        Utils.event.on(document, 'mousemove', (e) => {
            if (!state.isDragging) return;

            const deltaX = e.clientX - state.lastX;
            const deltaY = e.clientY - state.lastY;

            state.translateX += deltaX;
            state.translateY += deltaY;
            state.lastX = e.clientX;
            state.lastY = e.clientY;

            updateTransform();
        });

        Utils.event.on(document, 'mouseup', () => {
            if (state.isDragging) {
                state.isDragging = false;
                img.style.cursor = 'grab';
            }
        });

        // 鼠标滚轮缩放
        Utils.event.on(container, 'wheel', (e) => {
            e.preventDefault();

            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const newScale = state.scale * delta;

            if (newScale >= 0.1 && newScale <= 5) {
                state.scale = newScale;
                updateTransform();
            }
        });

        // 双击重置
        Utils.event.on(img, 'dblclick', () => {
            this.fitImageToContainer(img, container, state, updateTransform);
        });

        // 设置初始光标
        img.style.cursor = 'grab';
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        const loadingDiv = document.getElementById('loading-indicator') || this.createLoadingIndicator();
        loadingDiv.querySelector('.loading-text').textContent = message;
        loadingDiv.style.display = 'flex';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingDiv = document.getElementById('loading-indicator');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    /**
     * 创建加载指示器
     */
    createLoadingIndicator() {
        const loadingDiv = Utils.dom.create('div', {
            id: 'loading-indicator',
            className: 'loading-indicator',
            innerHTML: `
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            `,
            style: 'display: none;'
        });
        document.body.appendChild(loadingDiv);
        return loadingDiv;
    }





    /**
     * 分享文件
     */
    shareFile(fileId) {
        // TODO: 实现分享功能
        this.showToast('分享功能开发中...', 'info');
    }

    /**
     * 显示文件信息
     */
    showFileInfo(fileId) {
        // TODO: 实现文件信息功能
        this.showToast('文件信息功能开发中...', 'info');
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboard(event) {
        // Ctrl+A 全选
        if (event.ctrlKey && event.key === 'a') {
            event.preventDefault();
            this.selectAll();
        }
        
        // Delete 删除
        if (event.key === 'Delete') {
            this.deleteSelected();
        }
        
        // Escape 取消选择
        if (event.key === 'Escape') {
            this.clearSelection();
        }
    }
    
    /**
     * 全选文件
     */
    selectAll() {
        this.clearSelection();
        Utils.dom.$$('.file-item').forEach(item => {
            const fileId = item.dataset.fileId;
            this.selectFile(fileId, item);
        });
    }

    /**
     * 处理文件复选框变化
     */
    handleFileCheckboxChange(fileId, checked) {
        if (checked) {
            this.selectedForDownload.add(fileId);
        } else {
            this.selectedForDownload.delete(fileId);
        }

        // 更新批量操作按钮状态
        this.updateBatchActionButtons();
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActionButtons() {
        const selectedCount = this.selectedForDownload.size;

        // 如果没有批量操作按钮，创建它们
        this.ensureBatchActionButtons();

        const batchDownloadBtn = Utils.dom.$('#batch-download-btn');
        const selectAllBtn = Utils.dom.$('#select-all-btn');
        const clearSelectionBtn = Utils.dom.$('#clear-selection-btn');

        if (batchDownloadBtn) {
            batchDownloadBtn.disabled = selectedCount === 0;
            batchDownloadBtn.textContent = selectedCount > 0 ?
                `下载选中的 ${selectedCount} 个文件` : '批量下载';
        }

        if (selectAllBtn) {
            selectAllBtn.style.display = selectedCount === 0 ? 'inline-block' : 'none';
        }

        if (clearSelectionBtn) {
            clearSelectionBtn.style.display = selectedCount > 0 ? 'inline-block' : 'none';
        }
    }

    /**
     * 确保批量操作按钮存在
     */
    ensureBatchActionButtons() {
        // 检查是否已经存在批量操作按钮
        if (Utils.dom.$('#batch-download-btn')) return;

        // 查找视图控制区域
        const viewControls = Utils.dom.$('.view-controls');
        if (!viewControls) return;

        // 创建批量操作按钮容器
        const batchActionsContainer = Utils.dom.create('div', {
            className: 'batch-actions',
            innerHTML: `
                <button id="select-all-btn" class="btn btn-secondary">
                    <i class="fas fa-check-square"></i>
                    全选
                </button>
                <button id="clear-selection-btn" class="btn btn-secondary" style="display: none;">
                    <i class="fas fa-times"></i>
                    取消选择
                </button>
                <button id="batch-download-btn" class="btn btn-primary" disabled>
                    <i class="fas fa-download"></i>
                    批量下载
                </button>
            `
        });

        viewControls.appendChild(batchActionsContainer);

        // 绑定事件
        Utils.event.on(Utils.dom.$('#select-all-btn'), 'click', () => this.selectAllForDownload());
        Utils.event.on(Utils.dom.$('#clear-selection-btn'), 'click', () => this.clearDownloadSelection());
        Utils.event.on(Utils.dom.$('#batch-download-btn'), 'click', () => this.batchDownload());
    }

    /**
     * 全选文件用于下载
     */
    selectAllForDownload() {
        // 选中所有文件（不包括文件夹）
        Utils.dom.$$('.file-checkbox').forEach(checkbox => {
            checkbox.checked = true;
            const fileId = checkbox.dataset.fileId;
            this.selectedForDownload.add(fileId);
        });

        this.updateBatchActionButtons();
    }

    /**
     * 清除下载选择
     */
    clearDownloadSelection() {
        // 取消选中所有复选框
        Utils.dom.$$('.file-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        this.selectedForDownload.clear();
        this.updateBatchActionButtons();
    }

    /**
     * 批量下载文件
     */
    async batchDownload() {
        if (this.selectedForDownload.size === 0) {
            this.showToast('请先选择要下载的文件', 'warning');
            return;
        }

        // 防止重复下载
        if (this.isDownloading) {
            this.showToast('正在下载中，请稍候...', 'warning');
            return;
        }

        try {
            this.isDownloading = true;
            this.showToast('正在准备下载...', 'info');

            const fileIds = Array.from(this.selectedForDownload);

            // 使用新的批量下载接口
            const response = await fetch(`${api.getBaseURL()}/download/batch`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ file_ids: fileIds })
            });

            if (response.status === 423) {
                // 批量文件被加密，需要申请密码
                const errorData = await response.json();
                this.showToast('部分文件已加密，请单独下载并申请密码', 'warning');
                return;
            }

            if (!response.ok) {
                throw new Error(`批量下载失败: ${response.status}`);
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            // 获取文件名
            const contentDisposition = response.headers.get('content-disposition');
            let filename = `batch_download_${Date.now()}.zip`;
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) filename = matches[1];
            }

            Utils.url.downloadFile(url, filename);
            URL.revokeObjectURL(url);

            this.showToast(`成功下载 ${fileIds.length} 个文件`, 'success');

            // 清除选择
            this.clearDownloadSelection();

            // 刷新下载记录
            this.refreshDownloadRecords();

        } catch (error) {
            CONFIG.log('error', 'Batch download failed:', error);
            this.showToast('批量下载失败', 'error');
        } finally {
            this.isDownloading = false;
        }
    }

    /**
     * 下载文件夹
     */
    async downloadFolder(folderId) {
        // 防止重复下载 - 使用文件夹ID作为键
        const downloadKey = `folder_${folderId}`;
        if (this.isDownloading || this._activeDownloads?.has(downloadKey)) {
            CONFIG.log('warn', `文件夹 ${folderId} 正在下载中，忽略重复请求`);
            this.showToast('正在下载中，请稍候...', 'warning');
            return;
        }

        try {
            // 初始化活动下载集合
            if (!this._activeDownloads) {
                this._activeDownloads = new Set();
            }

            this.isDownloading = true;
            this._activeDownloads.add(downloadKey);

            CONFIG.log('info', `开始下载文件夹: ${folderId}`);
            this.showToast('正在打包文件夹...', 'info');

            // 调用后端API下载文件夹
            const response = await FileAPI.downloadFolder(folderId);

            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);

                // 获取文件名
                const contentDisposition = response.headers.get('content-disposition');
                let filename = `folder_${folderId}_${Date.now()}.zip`;
                if (contentDisposition) {
                    const matches = contentDisposition.match(/filename="(.+)"/);
                    if (matches) filename = matches[1];
                }

                Utils.url.downloadFile(url, filename);
                URL.revokeObjectURL(url);

                this.showToast('文件夹下载成功', 'success');
            } else {
                throw new Error('文件夹下载失败');
            }

        } catch (error) {
            CONFIG.log('error', 'Folder download failed:', error);
            this.showToast('文件夹下载失败', 'error');
        } finally {
            this.isDownloading = false;
            if (this._activeDownloads) {
                this._activeDownloads.delete(downloadKey);
            }
            CONFIG.log('info', `文件夹下载完成: ${folderId}`);
        }
    }
    
    /**
     * 删除选中的文件
     */
    async deleteSelected() {
        if (this.selectedFiles.size === 0) return;
        
        let confirmed = false;
        if (typeof Components !== 'undefined' && Components.Confirm) {
            confirmed = await Components.Confirm.show(
                `确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`,
                '确认删除'
            );
        } else {
            confirmed = confirm(`确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`);
        }
        
        if (confirmed) {
            // TODO: 实现删除功能
            this.showToast('删除功能开发中...', 'info');
        }
    }
    
    /**
     * 刷新文件列表
     */
    refresh() {
        // 根据当前视图状态决定刷新方式
        if (this.isInSearchMode) {
            // 如果在搜索模式，重新执行搜索
            if (window.searchManager && window.searchManager.lastSearchQuery) {
                window.searchManager.performSearch(window.searchManager.lastSearchQuery);
            } else {
                this.loadFiles(this.currentFolder?.id);
            }
        } else {
            // 正常文件夹视图，刷新文件列表
            this.loadFiles(this.currentFolder?.id);
        }
    }

    /**
     * 显示Toast消息
     */
    showToast(message, type = 'info') {
        // 防重复显示机制
        if (!this._lastToastMessages) {
            this._lastToastMessages = new Map();
        }

        const messageKey = `${type}:${message}`;
        const now = Date.now();
        const lastShown = this._lastToastMessages.get(messageKey);

        if (lastShown && (now - lastShown) < 3000) {
            console.log(`FileManager Toast重复消息被阻止: ${message}`);
            return;
        }

        this._lastToastMessages.set(messageKey, now);

        if (typeof Components !== 'undefined' && Components.Toast) {
            Components.Toast.show(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 刷新单个文件的缩略图
     */
    refreshThumbnail(fileId) {
        const thumbnailContainers = Utils.dom.$$(`[id^="thumb-${fileId}"]`);
        thumbnailContainers.forEach(container => {
            const img = container.querySelector('.thumbnail-image');
            const loading = container.querySelector('.thumbnail-loading');
            const fallback = container.querySelector('.thumbnail-fallback');

            if (img && loading && fallback) {
                // 重置状态
                loading.style.display = 'block';
                img.style.display = 'none';
                fallback.style.display = 'none';

                // 重新加载缩略图（添加时间戳避免缓存）
                const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'medium');
                img.src = `${thumbnailUrl}&t=${Date.now()}`;
            }
        });
    }

    /**
     * 批量刷新缩略图
     */
    refreshAllThumbnails() {
        const imageFiles = this.files.filter(file =>
            Utils.isImageFile(file.name) && file.type !== 'folder'
        );

        imageFiles.forEach(file => {
            this.refreshThumbnail(file.id);
        });

        this.showToast('正在刷新缩略图...', 'info');
    }

    /**
     * 创建文件夹卡片
     */
    createFolderCard(file) {
        const card = Utils.dom.create('div', {
            className: 'folder-card',
            'data-file-id': file.id,
            'data-file-type': file.type,
            'data-file-path': file.path
        });

        // 文件夹图标
        const iconDiv = Utils.dom.create('div', {
            className: 'folder-icon'
        });
        const icon = Utils.dom.create('i', {
            className: 'fas fa-folder'
        });
        iconDiv.appendChild(icon);

        // 文件夹名称
        const nameDiv = Utils.dom.create('div', {
            className: 'folder-name',
            textContent: file.name,
            title: file.name
        });

        card.appendChild(iconDiv);
        card.appendChild(nameDiv);

        // 添加点击事件
        card.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // 清除其他选中状态
            document.querySelectorAll('.folder-card.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置当前选中状态
            card.classList.add('selected');

            // 触发文件夹打开事件
            this.handleFileAction('open', file);
        });

        // 添加双击事件
        card.addEventListener('dblclick', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleFileAction('open', file);
        });

        return card;
    }

    /**
     * 创建文件详情卡片
     */
    createFileDetailCard(file) {
        const isImage = Utils.isImageFile(file.name);
        const icon = CONFIG.FILES.getFileIcon(file.name, false);

        const card = Utils.dom.create('div', {
            className: 'file-detail-card',
            'data-file-id': file.id,
            'data-file-type': file.type,
            'data-file-path': file.path
        });

        // 文件图标
        const iconDiv = Utils.dom.create('div', {
            className: 'file-icon'
        });
        const iconElement = Utils.dom.create('i', {
            className: icon
        });
        iconDiv.appendChild(iconElement);

        // 文件信息
        const infoDiv = Utils.dom.create('div', {
            className: 'file-info'
        });

        // 文件名
        const nameDiv = Utils.dom.create('div', {
            className: 'file-name',
            textContent: file.name,
            title: file.name
        });

        // 文件元数据
        const metaDiv = Utils.dom.create('div', {
            className: 'file-meta'
        });

        // 修改时间
        const timeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const timeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '修改时间'
        });
        const timeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: file.modified || '-'
        });
        timeItem.appendChild(timeLabel);
        timeItem.appendChild(timeValue);

        // 文件类型
        const typeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const typeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '类型'
        });
        const typeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: this.getFileTypeDescription(file.name)
        });
        typeItem.appendChild(typeLabel);
        typeItem.appendChild(typeValue);

        // 文件大小
        const sizeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const sizeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '大小'
        });
        const sizeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: file.size || '-'
        });
        sizeItem.appendChild(sizeLabel);
        sizeItem.appendChild(sizeValue);

        metaDiv.appendChild(timeItem);
        metaDiv.appendChild(typeItem);
        metaDiv.appendChild(sizeItem);

        infoDiv.appendChild(nameDiv);
        infoDiv.appendChild(metaDiv);

        // 操作按钮
        const actionsDiv = Utils.dom.create('div', {
            className: 'file-actions'
        });

        // 下载按钮
        const downloadBtn = Utils.dom.create('button', {
            className: 'action-btn',
            'data-action': 'download',
            title: '下载'
        });
        downloadBtn.innerHTML = '<i class="fas fa-download"></i>';
        // 不需要直接绑定事件，使用事件委托（在bindEvents中已处理）

        // 预览按钮（仅图片）
        if (isImage) {
            const previewBtn = Utils.dom.create('button', {
                className: 'action-btn',
                'data-action': 'preview',
                title: '预览'
            });
            previewBtn.innerHTML = '<i class="fas fa-eye"></i>';
            // 不需要直接绑定事件，使用事件委托（在bindEvents中已处理）
            actionsDiv.appendChild(previewBtn);
        }

        // 收藏按钮
        const favoriteBtn = Utils.dom.create('button', {
            className: 'action-btn',
            'data-action': 'favorite',
            title: '收藏'
        });
        favoriteBtn.innerHTML = '<i class="fas fa-star"></i>';
        // 不需要直接绑定事件，使用事件委托（在bindEvents中已处理）

        actionsDiv.appendChild(downloadBtn);
        actionsDiv.appendChild(favoriteBtn);

        card.appendChild(iconDiv);
        card.appendChild(infoDiv);
        card.appendChild(actionsDiv);

        // 添加点击事件
        card.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // 清除其他选中状态
            document.querySelectorAll('.file-detail-card.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置当前选中状态
            card.classList.add('selected');
        });

        return card;
    }

    /**
     * 获取文件类型描述
     */
    getFileTypeDescription(fileName) {
        const ext = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'jpg': 'JPG 图片文件',
            'jpeg': 'JPEG 图片文件',
            'png': 'PNG 图片文件',
            'gif': 'GIF 图片文件',
            'bmp': 'BMP 图片文件',
            'tif': 'TIF 图片文件',
            'tiff': 'TIFF 图片文件',
            'psd': 'PSD 图片文件',
            'ai': 'AI 图片文件',
            'eps': 'EPS 图片文件'
        };
        return typeMap[ext] || `${ext.toUpperCase()} 文件`;
    }

    /**
     * 显示密码申请对话框
     */
    showPasswordRequestDialog(fileId, fileName, isFromRecord = false) {
        // 创建对话框HTML
        const dialogHtml = `
            <div class="password-request-dialog">
                <div class="dialog-header">
                    <h3>申请解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-request-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="file-info">
                        <i class="fas fa-file-archive"></i>
                        <span class="file-name">${fileName}</span>
                    </div>
                    <div class="info-text">
                        <p>该文件已加密，需要密码才能解压。</p>
                        <p>请填写申请原因（可选）：</p>
                    </div>
                    <textarea class="reason-input" placeholder="请简要说明申请密码的原因..." maxlength="200"></textarea>
                    <div class="remaining-requests">
                        <span class="remaining-count">剩余申请次数：<span id="remaining-count">-</span></span>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-secondary" onclick="this.closest('.password-request-dialog').remove()">
                        取消
                    </button>
                    <button class="btn btn-primary" onclick="fileManager.submitPasswordRequest(${fileId}, this, ${isFromRecord})">
                        申请密码
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 聚焦到文本框
        const reasonInput = overlay.querySelector('.reason-input');
        if (reasonInput) {
            reasonInput.focus();
        }

        // 添加ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        });
    }

    /**
     * 提交密码申请
     */
    async submitPasswordRequest(fileId, buttonElement, isFromRecord = false) {
        try {
            const dialog = buttonElement.closest('.password-request-dialog');
            const reasonInput = dialog.querySelector('.reason-input');
            const reason = reasonInput.value.trim();

            // 禁用按钮
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 申请中...';

            // 调用API申请密码
            const result = await FileAPI.requestDownloadPassword(fileId, reason);

            if (result.success) {
                // 显示密码
                this.showPasswordResult(result.data.password, result.data.remaining_requests);

                // 关闭申请对话框
                dialog.closest('.dialog-overlay').remove();

                this.showToast('密码申请成功！', 'success');
            } else {
                throw new Error(result.error || '密码申请失败');
            }

        } catch (error) {
            console.error('密码申请失败:', error);
            this.showToast(error.message || '密码申请失败', 'error');

            // 恢复按钮状态
            buttonElement.disabled = false;
            buttonElement.innerHTML = '申请密码';
        }
    }

    /**
     * 显示密码结果
     */
    showPasswordResult(password, remainingRequests) {
        const dialogHtml = `
            <div class="password-result-dialog">
                <div class="dialog-header">
                    <h3>解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-result-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="password-display">
                        <label>解压密码：</label>
                        <div class="password-box">
                            <input type="text" value="${password}" readonly class="password-input">
                            <button class="copy-btn" onclick="fileManager.copyPassword('${password}', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="password-info">
                        <p><i class="fas fa-info-circle"></i> 请妥善保管此密码，用于解压下载的文件。</p>
                        <p><i class="fas fa-clock"></i> 密码有效期：24小时</p>
                        <p><i class="fas fa-exclamation-triangle"></i> 剩余申请次数：${remainingRequests}</p>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-primary" onclick="this.closest('.password-result-dialog').remove()">
                        确定
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 自动选中密码
        const passwordInput = overlay.querySelector('.password-input');
        if (passwordInput) {
            passwordInput.select();
        }
    }

    /**
     * 复制密码到剪贴板
     */
    async copyPassword(password, buttonElement) {
        try {
            await navigator.clipboard.writeText(password);

            // 更新按钮状态
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i>';
            buttonElement.classList.add('copied');

            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
                buttonElement.classList.remove('copied');
            }, 2000);

            this.showToast('密码已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showToast('复制失败，请手动复制', 'error');
        }
    }

    /**
     * 切换视图
     */
    switchView(view) {
        this.currentView = view;

        // 更新导航状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`[data-view="${view}"]`)?.closest('.menu-item');
        if (activeItem) {
            activeItem.classList.add('active');
        }

        // 根据视图显示不同内容
        switch (view) {
            case 'downloads':
                this.showDownloadRecords();
                break;
            case 'recent':
                this.showRecentFiles();
                break;
            default:
                this.showHomeView();
                break;
        }
    }

    /**
     * 显示下载记录
     */
    async showDownloadRecords() {
        console.log('=== 开始显示下载记录 ===');

        try {
            // 更新状态
            this.currentView = 'downloads';
            console.log('设置当前视图为downloads');

            // 更新面包屑
            this.updateBreadcrumb([
                { name: '首页', icon: 'fas fa-home' },
                { name: '下载记录', icon: 'fas fa-download' }
            ]);
            console.log('更新面包屑完成');

            // 显示下载记录视图
            this.showView('download-records-view');
            console.log('显示下载记录视图完成');

            // 初始化下载记录界面
            this.initDownloadRecordsInterface();

            // 加载下载记录数据
            await this.loadDownloadRecordsData();

        } catch (error) {
            console.error('显示下载记录失败:', error);
            this.showToast('获取下载记录失败: ' + error.message, 'error');
            this.showDownloadRecordsError(error.message);
        }
    }

    /**
     * 初始化下载记录界面
     */
    initDownloadRecordsInterface() {
        const container = document.getElementById('download-records-view');
        if (!container) {
            console.error('找不到下载记录容器');
            return;
        }

        // 创建完整的下载记录界面
        container.innerHTML = `
            <div class="download-records-wrapper">
                <!-- 头部区域 -->
                <div class="download-records-header">
                    <div class="header-left">
                        <h2>
                            <i class="fas fa-download"></i>
                            我的下载记录
                        </h2>
                        <div class="record-summary" id="record-summary">
                            <span class="summary-item">
                                <i class="fas fa-file"></i>
                                总下载: <span id="total-downloads">-</span>
                            </span>
                            <span class="summary-item">
                                <i class="fas fa-hdd"></i>
                                总大小: <span id="total-size">-</span>
                            </span>
                            <span class="summary-item">
                                <i class="fas fa-calendar"></i>
                                最近: <span id="last-download">-</span>
                            </span>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="download-controls">
                            <button class="btn btn-secondary" id="toggle-filters">
                                <i class="fas fa-filter"></i>
                                筛选
                            </button>
                            <button class="btn btn-primary" id="refresh-downloads">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 筛选面板 -->
                <div class="filter-panel hidden" id="filter-panel">
                    <div class="filter-content">
                        <div class="filter-row">
                            <div class="filter-group">
                                <label>下载类型:</label>
                                <select id="filter-type">
                                    <option value="">全部类型</option>
                                    <option value="single">单文件</option>
                                    <option value="batch">批量下载</option>
                                    <option value="folder">文件夹</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>加密状态:</label>
                                <select id="filter-encrypted">
                                    <option value="">全部</option>
                                    <option value="true">已加密</option>
                                    <option value="false">未加密</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>时间范围:</label>
                                <select id="filter-time-range">
                                    <option value="">全部时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="filter-group custom-date hidden" id="custom-date-group">
                                <label>自定义日期:</label>
                                <input type="date" id="filter-date-from" placeholder="开始日期">
                                <span>至</span>
                                <input type="date" id="filter-date-to" placeholder="结束日期">
                            </div>
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-primary" id="apply-filters">
                                <i class="fas fa-search"></i>
                                应用筛选
                            </button>
                            <button class="btn btn-secondary" id="clear-filters">
                                <i class="fas fa-times"></i>
                                清除筛选
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主内容区域 -->
                <div class="download-records-main">
                    <!-- 加载状态 -->
                    <div class="loading-state" id="loading-state">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                        <p>正在加载下载记录...</p>
                    </div>

                    <!-- 空状态 -->
                    <div class="empty-state hidden" id="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h3>暂无下载记录</h3>
                        <p>您还没有下载过任何文件</p>
                        <button class="btn btn-primary" onclick="fileManager.switchToView('files')">
                            <i class="fas fa-folder"></i>
                            浏览文件
                        </button>
                    </div>

                    <!-- 错误状态 -->
                    <div class="error-state hidden" id="error-state">
                        <div class="error-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3>加载失败</h3>
                        <p id="error-message">无法加载下载记录</p>
                        <button class="btn btn-primary" onclick="fileManager.loadDownloadRecordsData()">
                            <i class="fas fa-redo"></i>
                            重试
                        </button>
                    </div>

                    <!-- 记录列表 -->
                    <div class="records-list hidden" id="records-list">
                        <!-- 记录内容将在这里动态生成 -->
                    </div>

                    <!-- 分页控件 -->
                    <div class="pagination-wrapper hidden" id="pagination-wrapper">
                        <div class="pagination-info">
                            <span id="pagination-text">显示第 1-20 条，共 0 条记录</span>
                        </div>
                        <div class="pagination-controls">
                            <button class="btn btn-sm" id="first-page" disabled>
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button class="btn btn-sm" id="prev-page" disabled>
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <div class="page-numbers" id="page-numbers">
                                <!-- 页码按钮将在这里生成 -->
                            </div>
                            <button class="btn btn-sm" id="next-page" disabled>
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button class="btn btn-sm" id="last-page" disabled>
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定事件监听器
        this.bindDownloadRecordsEvents();
    }

    /**
     * 绑定下载记录相关事件
     */
    bindDownloadRecordsEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-downloads');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadDownloadRecordsData();
            });
        }

        // 筛选切换按钮
        const filterToggle = document.getElementById('toggle-filters');
        const filterPanel = document.getElementById('filter-panel');
        if (filterToggle && filterPanel) {
            filterToggle.addEventListener('click', () => {
                filterPanel.classList.toggle('hidden');
                const icon = filterToggle.querySelector('i');
                if (filterPanel.classList.contains('hidden')) {
                    icon.className = 'fas fa-filter';
                } else {
                    icon.className = 'fas fa-filter-circle-xmark';
                }
            });
        }

        // 时间范围选择
        const timeRangeSelect = document.getElementById('filter-time-range');
        const customDateGroup = document.getElementById('custom-date-group');
        if (timeRangeSelect && customDateGroup) {
            timeRangeSelect.addEventListener('change', (e) => {
                if (e.target.value === 'custom') {
                    customDateGroup.classList.remove('hidden');
                } else {
                    customDateGroup.classList.add('hidden');
                }
            });
        }

        // 应用筛选
        const applyFilters = document.getElementById('apply-filters');
        if (applyFilters) {
            applyFilters.addEventListener('click', () => {
                this.applyDownloadFilters();
            });
        }

        // 清除筛选
        const clearFilters = document.getElementById('clear-filters');
        if (clearFilters) {
            clearFilters.addEventListener('click', () => {
                this.clearDownloadFilters();
            });
        }

        // 分页控件
        this.bindPaginationEvents();
    }

    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        const firstPage = document.getElementById('first-page');
        const prevPage = document.getElementById('prev-page');
        const nextPage = document.getElementById('next-page');
        const lastPage = document.getElementById('last-page');

        if (firstPage) {
            firstPage.addEventListener('click', () => this.goToPage(1));
        }
        if (prevPage) {
            prevPage.addEventListener('click', () => this.goToPreviousPage());
        }
        if (nextPage) {
            nextPage.addEventListener('click', () => this.goToNextPage());
        }
        if (lastPage) {
            lastPage.addEventListener('click', () => this.goToLastPage());
        }
    }

    /**
     * 加载下载记录数据
     */
    async loadDownloadRecordsData(page = 1, pageSize = 20, filters = {}) {
        console.log('开始加载下载记录数据...', { page, pageSize, filters });

        // 显示加载状态
        this.showDownloadRecordsLoading();

        try {
            // 检查API是否可用
            if (typeof DownloadAPI === 'undefined') {
                throw new Error('DownloadAPI未定义，请检查API模块是否正确加载');
            }

            // 调用API获取下载记录
            console.log('调用DownloadAPI.getDownloadRecords...');
            const response = await DownloadAPI.getDownloadRecords(page, pageSize);
            console.log('API响应:', response);

            // 验证响应数据
            if (!response) {
                throw new Error('API返回空响应');
            }

            if (response.success === false) {
                throw new Error(response.error || '获取下载记录失败');
            }

            // 提取数据
            const records = response.records || response.data || [];
            const total = response.total || 0;
            const currentPage = response.page || page;
            const limit = response.limit || pageSize;

            console.log(`成功获取 ${records.length} 条记录，总计 ${total} 条`);

            // 更新界面
            if (records.length > 0) {
                this.renderDownloadRecordsList(records);
                this.updateDownloadSummary(records, total);
                this.updatePagination(currentPage, total, limit);
                this.showDownloadRecordsContent();
            } else {
                this.showDownloadRecordsEmpty();
            }

            // 保存当前状态
            this.currentDownloadPage = currentPage;
            this.currentDownloadFilters = filters;

        } catch (error) {
            console.error('加载下载记录失败:', error);
            this.showDownloadRecordsError(error.message);
            Components.Toast.error(`加载下载记录失败: ${error.message}`);
        }
    }

    /**
     * 显示加载状态
     */
    showDownloadRecordsLoading() {
        this.hideAllDownloadStates();
        const loadingState = document.getElementById('loading-state');
        if (loadingState) {
            loadingState.classList.remove('hidden');
        }
    }

    /**
     * 显示空状态
     */
    showDownloadRecordsEmpty() {
        this.hideAllDownloadStates();
        const emptyState = document.getElementById('empty-state');
        if (emptyState) {
            emptyState.classList.remove('hidden');
        }
    }

    /**
     * 显示错误状态
     */
    showDownloadRecordsError(message) {
        this.hideAllDownloadStates();
        const errorState = document.getElementById('error-state');
        const errorMessage = document.getElementById('error-message');
        if (errorState) {
            errorState.classList.remove('hidden');
        }
        if (errorMessage) {
            errorMessage.textContent = message || '未知错误';
        }
    }

    /**
     * 显示记录内容
     */
    showDownloadRecordsContent() {
        this.hideAllDownloadStates();
        const recordsList = document.getElementById('records-list');
        const paginationWrapper = document.getElementById('pagination-wrapper');
        if (recordsList) {
            recordsList.classList.remove('hidden');
        }
        if (paginationWrapper) {
            paginationWrapper.classList.remove('hidden');
        }
    }

    /**
     * 隐藏所有状态
     */
    hideAllDownloadStates() {
        const states = ['loading-state', 'empty-state', 'error-state', 'records-list', 'pagination-wrapper'];
        states.forEach(stateId => {
            const element = document.getElementById(stateId);
            if (element) {
                element.classList.add('hidden');
            }
        });
    }

    /**
     * 渲染下载记录列表
     */
    renderDownloadRecordsList(records) {
        const recordsList = document.getElementById('records-list');
        if (!recordsList) {
            console.error('找不到记录列表容器');
            return;
        }

        console.log('开始渲染下载记录列表，记录数:', records.length);

        // 按日期分组记录
        const groupedRecords = this.groupDownloadRecordsByDate(records);

        let html = '<div class="download-records-container">';

        // 渲染每个日期组
        for (const [date, dateRecords] of Object.entries(groupedRecords)) {
            if (dateRecords.length > 0) {
                html += `
                    <div class="download-group">
                        <div class="group-header">
                            <h3 class="group-date">${this.formatDownloadDate(date)}</h3>
                            <span class="group-count">${dateRecords.length} 条记录</span>
                        </div>
                        <div class="group-records">
                `;

                // 渲染该日期的所有记录
                dateRecords.forEach(record => {
                    html += this.renderDownloadRecordCard(record);
                });

                html += `
                        </div>
                    </div>
                `;
            }
        }

        html += '</div>';
        recordsList.innerHTML = html;

        console.log('下载记录列表渲染完成');
    }

    /**
     * 按日期分组下载记录
     */
    groupDownloadRecordsByDate(records) {
        const groups = {};

        records.forEach(record => {
            try {
                // 获取下载时间，优先使用downloaded_at，其次created_at
                const downloadTime = record.downloaded_at || record.created_at || new Date().toISOString();
                const date = new Date(downloadTime).toDateString();

                if (!groups[date]) {
                    groups[date] = [];
                }
                groups[date].push(record);
            } catch (error) {
                console.warn('处理记录分组时出错:', error, record);
            }
        });

        // 按日期排序（最新的在前）
        const sortedGroups = {};
        Object.keys(groups)
            .sort((a, b) => new Date(b) - new Date(a))
            .forEach(date => {
                // 每组内按时间排序（最新的在前）
                groups[date].sort((a, b) => {
                    const timeA = new Date(a.downloaded_at || a.created_at || 0);
                    const timeB = new Date(b.downloaded_at || b.created_at || 0);
                    return timeB - timeA;
                });
                sortedGroups[date] = groups[date];
            });

        return sortedGroups;
    }

    /**
     * 渲染单个下载记录卡片
     */
    renderDownloadRecordCard(record) {
        try {
            // 提取记录信息
            const recordId = record.id || 'unknown';
            const fileId = record.file_id || '';
            const filename = record.zip_filename || record.filename || '未知文件';
            const fileSize = record.file_size || 0;
            const downloadTime = record.downloaded_at || record.created_at || new Date().toISOString();
            const downloadType = record.download_type || 'single';
            const isEncrypted = Boolean(record.is_encrypted);
            const downloadStatus = record.download_status || 'completed';
            const ipAddress = record.ip_address || '';

            // 获取文件图标
            const fileIcon = this.getDownloadFileIcon(filename, downloadType);

            // 格式化信息
            const formattedSize = this.formatFileSize(fileSize);
            const formattedTime = this.formatDownloadTime(downloadTime);
            const typeLabel = this.getDownloadTypeLabel(downloadType);

            return `
                <div class="download-record-card" data-record-id="${recordId}">
                    <div class="record-main">
                        <div class="record-icon">
                            <i class="${fileIcon}"></i>
                            ${isEncrypted ? '<div class="encryption-badge"><i class="fas fa-lock"></i></div>' : ''}
                        </div>

                        <div class="record-info">
                            <div class="record-title">
                                <h4 class="filename" title="${filename}">${this.truncateFilename(filename, 40)}</h4>
                                <div class="record-badges">
                                    <span class="type-badge type-${downloadType}">${typeLabel}</span>
                                    ${downloadStatus !== 'completed' ? `<span class="status-badge status-${downloadStatus}">${this.getStatusLabel(downloadStatus)}</span>` : ''}
                                </div>
                            </div>

                            <div class="record-meta">
                                <span class="meta-item">
                                    <i class="fas fa-hdd"></i>
                                    ${formattedSize}
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    ${formattedTime}
                                </span>
                                ${ipAddress ? `
                                    <span class="meta-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        ${ipAddress}
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="record-actions">
                        ${this.renderRecordActions(record)}
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('渲染下载记录卡片失败:', error, record);
            return `
                <div class="download-record-card error">
                    <div class="record-main">
                        <div class="record-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="record-info">
                            <div class="record-title">
                                <h4 class="filename">记录显示错误</h4>
                            </div>
                            <div class="record-meta">
                                <span class="meta-item">无法显示此记录</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 渲染记录操作按钮
     */
    renderRecordActions(record) {
        const fileId = record.file_id || '';
        const recordId = record.id || '';
        const isEncrypted = Boolean(record.is_encrypted);
        const downloadStatus = record.download_status || 'completed';
        const filename = record.zip_filename || record.filename || '';

        let actions = [];

        // 根据状态和类型显示不同操作
        if (downloadStatus === 'completed') {
            if (isEncrypted) {
                actions.push(`
                    <button class="btn btn-sm btn-primary" onclick="fileManager.requestPasswordForRecord('${fileId}', '${filename.replace(/'/g, "\\'")}')">
                        <i class="fas fa-key"></i>
                        申请密码
                    </button>
                `);
            } else if (fileId) {
                actions.push(`
                    <button class="btn btn-sm btn-secondary" onclick="fileManager.redownloadFile('${fileId}')">
                        <i class="fas fa-download"></i>
                        重新下载
                    </button>
                `);
            }
        }

        // 详情按钮
        actions.push(`
            <button class="btn btn-sm btn-outline" onclick="fileManager.showDownloadRecordDetails('${recordId}')">
                <i class="fas fa-info-circle"></i>
                详情
            </button>
        `);

        return actions.join('');
    }

    /**
     * 更新下载统计摘要
     */
    updateDownloadSummary(records, total) {
        try {
            // 计算统计信息
            const totalSize = records.reduce((sum, record) => sum + (record.file_size || 0), 0);
            const lastDownload = records.length > 0 ?
                Math.max(...records.map(r => new Date(r.downloaded_at || r.created_at || 0).getTime())) : 0;

            // 更新显示
            const totalDownloadsEl = document.getElementById('total-downloads');
            const totalSizeEl = document.getElementById('total-size');
            const lastDownloadEl = document.getElementById('last-download');

            if (totalDownloadsEl) {
                totalDownloadsEl.textContent = total.toLocaleString();
            }
            if (totalSizeEl) {
                totalSizeEl.textContent = this.formatFileSize(totalSize);
            }
            if (lastDownloadEl) {
                if (lastDownload > 0) {
                    lastDownloadEl.textContent = this.formatRelativeTime(new Date(lastDownload));
                } else {
                    lastDownloadEl.textContent = '无';
                }
            }
        } catch (error) {
            console.error('更新下载统计失败:', error);
        }
    }

    /**
     * 更新分页控件
     */
    updatePagination(currentPage, total, pageSize) {
        const totalPages = Math.ceil(total / pageSize);
        const startRecord = (currentPage - 1) * pageSize + 1;
        const endRecord = Math.min(currentPage * pageSize, total);

        // 更新分页信息文本
        const paginationText = document.getElementById('pagination-text');
        if (paginationText) {
            paginationText.textContent = `显示第 ${startRecord}-${endRecord} 条，共 ${total} 条记录`;
        }

        // 更新分页按钮状态
        const firstPage = document.getElementById('first-page');
        const prevPage = document.getElementById('prev-page');
        const nextPage = document.getElementById('next-page');
        const lastPage = document.getElementById('last-page');

        if (firstPage) firstPage.disabled = currentPage <= 1;
        if (prevPage) prevPage.disabled = currentPage <= 1;
        if (nextPage) nextPage.disabled = currentPage >= totalPages;
        if (lastPage) lastPage.disabled = currentPage >= totalPages;

        // 生成页码按钮
        this.generatePageNumbers(currentPage, totalPages);

        // 保存分页状态
        this.currentDownloadPage = currentPage;
        this.totalDownloadPages = totalPages;
        this.downloadPageSize = pageSize;
    }

    /**
     * 生成页码按钮
     */
    generatePageNumbers(currentPage, totalPages) {
        const pageNumbers = document.getElementById('page-numbers');
        if (!pageNumbers) return;

        let html = '';
        const maxVisible = 5; // 最多显示5个页码
        let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(totalPages, startPage + maxVisible - 1);

        // 调整起始页
        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        // 生成页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            html += `
                <button class="btn btn-sm page-number ${isActive ? 'active' : ''}"
                        onclick="fileManager.goToPage(${i})"
                        ${isActive ? 'disabled' : ''}>
                    ${i}
                </button>
            `;
        }

        pageNumbers.innerHTML = html;
    }

    /**
     * 跳转到指定页
     */
    goToPage(page) {
        if (page < 1 || page > this.totalDownloadPages) return;
        this.loadDownloadRecordsData(page, this.downloadPageSize, this.currentDownloadFilters);
    }

    /**
     * 跳转到上一页
     */
    goToPreviousPage() {
        if (this.currentDownloadPage > 1) {
            this.goToPage(this.currentDownloadPage - 1);
        }
    }

    /**
     * 跳转到下一页
     */
    goToNextPage() {
        if (this.currentDownloadPage < this.totalDownloadPages) {
            this.goToPage(this.currentDownloadPage + 1);
        }
    }

    /**
     * 跳转到最后一页
     */
    goToLastPage() {
        this.goToPage(this.totalDownloadPages);
    }

    /**
     * 应用筛选条件
     */
    applyDownloadFilters() {
        const filters = this.getDownloadFilters();
        console.log('应用筛选条件:', filters);
        this.loadDownloadRecordsData(1, this.downloadPageSize || 20, filters);
    }

    /**
     * 获取当前筛选条件
     */
    getDownloadFilters() {
        const filters = {};

        // 下载类型
        const typeFilter = document.getElementById('filter-type');
        if (typeFilter && typeFilter.value) {
            filters.download_type = typeFilter.value;
        }

        // 加密状态
        const encryptedFilter = document.getElementById('filter-encrypted');
        if (encryptedFilter && encryptedFilter.value) {
            filters.is_encrypted = encryptedFilter.value === 'true';
        }

        // 时间范围
        const timeRangeFilter = document.getElementById('filter-time-range');
        if (timeRangeFilter && timeRangeFilter.value) {
            const timeRange = timeRangeFilter.value;
            const now = new Date();

            switch (timeRange) {
                case 'today':
                    filters.date_from = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
                    break;
                case 'week':
                    const weekStart = new Date(now);
                    weekStart.setDate(now.getDate() - now.getDay());
                    filters.date_from = weekStart.toISOString();
                    break;
                case 'month':
                    filters.date_from = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
                    break;
                case 'custom':
                    const dateFrom = document.getElementById('filter-date-from');
                    const dateTo = document.getElementById('filter-date-to');
                    if (dateFrom && dateFrom.value) {
                        filters.date_from = new Date(dateFrom.value).toISOString();
                    }
                    if (dateTo && dateTo.value) {
                        filters.date_to = new Date(dateTo.value + 'T23:59:59').toISOString();
                    }
                    break;
            }
        }

        return filters;
    }

    /**
     * 清除筛选条件
     */
    clearDownloadFilters() {
        // 重置筛选表单
        const typeFilter = document.getElementById('filter-type');
        const encryptedFilter = document.getElementById('filter-encrypted');
        const timeRangeFilter = document.getElementById('filter-time-range');
        const dateFromFilter = document.getElementById('filter-date-from');
        const dateToFilter = document.getElementById('filter-date-to');
        const customDateGroup = document.getElementById('custom-date-group');

        if (typeFilter) typeFilter.value = '';
        if (encryptedFilter) encryptedFilter.value = '';
        if (timeRangeFilter) timeRangeFilter.value = '';
        if (dateFromFilter) dateFromFilter.value = '';
        if (dateToFilter) dateToFilter.value = '';
        if (customDateGroup) customDateGroup.classList.add('hidden');

        // 重新加载数据
        this.loadDownloadRecordsData(1, this.downloadPageSize || 20, {});
    }

    /**
     * 获取下载文件图标
     */
    getDownloadFileIcon(filename, downloadType) {
        if (downloadType === 'folder') {
            return 'fas fa-folder-open';
        }
        if (downloadType === 'batch') {
            return 'fas fa-layer-group';
        }

        // 根据文件扩展名返回图标
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'zip': 'fas fa-file-archive',
            'rar': 'fas fa-file-archive',
            '7z': 'fas fa-file-archive',
            'tar': 'fas fa-file-archive',
            'gz': 'fas fa-file-archive',
            'pdf': 'fas fa-file-pdf',
            'doc': 'fas fa-file-word',
            'docx': 'fas fa-file-word',
            'xls': 'fas fa-file-excel',
            'xlsx': 'fas fa-file-excel',
            'ppt': 'fas fa-file-powerpoint',
            'pptx': 'fas fa-file-powerpoint',
            'jpg': 'fas fa-file-image',
            'jpeg': 'fas fa-file-image',
            'png': 'fas fa-file-image',
            'gif': 'fas fa-file-image',
            'mp4': 'fas fa-file-video',
            'avi': 'fas fa-file-video',
            'mov': 'fas fa-file-video',
            'mp3': 'fas fa-file-audio',
            'wav': 'fas fa-file-audio',
            'txt': 'fas fa-file-alt',
            'md': 'fas fa-file-alt'
        };

        return iconMap[ext] || 'fas fa-file';
    }

    /**
     * 获取下载类型标签
     */
    getDownloadTypeLabel(downloadType) {
        const typeMap = {
            'single': '单文件',
            'batch': '批量',
            'folder': '文件夹'
        };
        return typeMap[downloadType] || downloadType;
    }

    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
        const statusMap = {
            'pending': '准备中',
            'completed': '已完成',
            'expired': '已过期',
            'failed': '失败'
        };
        return statusMap[status] || status;
    }

    /**
     * 截断文件名
     */
    truncateFilename(filename, maxLength) {
        if (filename.length <= maxLength) {
            return filename;
        }

        const ext = filename.split('.').pop();
        const name = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = name.substring(0, maxLength - ext.length - 4) + '...';

        return truncatedName + '.' + ext;
    }

    /**
     * 格式化下载日期
     */
    formatDownloadDate(dateString) {
        try {
            const date = new Date(dateString);
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            if (date.toDateString() === today.toDateString()) {
                return '今天';
            } else if (date.toDateString() === yesterday.toDateString()) {
                return '昨天';
            } else {
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long'
                });
            }
        } catch (error) {
            console.warn('格式化日期失败:', dateString, error);
            return '未知日期';
        }
    }

    /**
     * 格式化下载时间
     */
    formatDownloadTime(timeString) {
        try {
            const date = new Date(timeString);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            console.warn('格式化时间失败:', timeString, error);
            return '未知时间';
        }
    }

    /**
     * 格式化相对时间
     */
    formatRelativeTime(date) {
        try {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) {
                return '刚刚';
            } else if (diffMins < 60) {
                return `${diffMins}分钟前`;
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        } catch (error) {
            console.warn('格式化相对时间失败:', date, error);
            return '未知时间';
        }
    }

    /**
     * 显示下载记录详情
     */
    async showDownloadRecordDetails(recordId) {
        try {
            // TODO: 实现详情对话框
            console.log('显示下载记录详情:', recordId);
            Components.Toast.info('记录详情功能开发中');
        } catch (error) {
            console.error('显示记录详情失败:', error);
            Components.Toast.error('显示记录详情失败');
        }
    }

    /**
     * 显示指定视图
     */
    showView(viewId) {
        // 隐藏所有视图
        const views = ['file-grid', 'file-list', 'download-records-view'];
        views.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.add('hidden');
            }
        });

        // 如果是下载记录视图，还需要隐藏其他界面元素
        if (viewId === 'download-records-view') {
            this.hideFileManagerElements();
            
            // 特别隐藏面包屑中的视图控制
            const viewControls = document.querySelector('.view-controls');
            if (viewControls) {
                viewControls.style.display = 'none';
            }
            
            // 更新状态
            this.currentView = 'downloads';
        } else {
            // 恢复文件管理界面元素
            this.showFileManagerElements();
            
            // 显示面包屑中的视图控制
            const viewControls = document.querySelector('.view-controls');
            if (viewControls) {
                viewControls.style.display = '';
            }
            
            // 根据视图类型更新状态
            if (viewId === 'file-grid' || viewId === 'file-list') {
                this.currentView = 'files';
            }
        }

        // 显示指定视图
        const targetView = document.getElementById(viewId);
        if (targetView) {
            targetView.classList.remove('hidden');
        }
    }

    /**
     * 隐藏文件管理器相关元素
     */
    hideFileManagerElements() {
        // 隐藏工具栏
        const toolbar = document.querySelector('.toolbar');
        if (toolbar) {
            toolbar.style.display = 'none';
        }

        // 隐藏面包屑导航（如果不想显示的话）
        // const breadcrumb = document.querySelector('.breadcrumb');
        // if (breadcrumb) {
        //     breadcrumb.style.display = 'none';
        // }

        // 隐藏批量操作按钮
        const batchActions = document.querySelector('.batch-actions');
        if (batchActions) {
            batchActions.style.display = 'none';
        }


    }

    /**
     * 显示文件管理器相关元素
     */
    showFileManagerElements() {
        // 显示工具栏
        const toolbar = document.querySelector('.toolbar');
        if (toolbar) {
            toolbar.style.display = '';
        }

        // 显示面包屑导航
        // const breadcrumb = document.querySelector('.breadcrumb');
        // if (breadcrumb) {
        //     breadcrumb.style.display = '';
        // }

        // 显示批量操作按钮
        const batchActions = document.querySelector('.batch-actions');
        if (batchActions) {
            batchActions.style.display = '';
        }



        // 确保文件网格和列表视图容器正确显示
        const fileGrid = document.getElementById('file-grid');
        const fileList = document.getElementById('file-list');
        
        // 根据当前视图模式显示相应容器
        if (this.currentViewMode === 'list') {
            if (fileGrid) fileGrid.classList.add('hidden');
            if (fileList) fileList.classList.remove('hidden');
        } else {
            if (fileList) fileList.classList.add('hidden');
            if (fileGrid) fileGrid.classList.remove('hidden');
        }
        
        // 确保视图控制按钮状态正确
        this.updateViewModeButtons();
    }

    /**
     * 获取下载记录
     */
    async getDownloadRecords() {
        console.log('=== 开始获取下载记录 ===');

        try {
            // 检查DownloadAPI是否可用
            if (typeof DownloadAPI === 'undefined') {
                throw new Error('DownloadAPI未定义');
            }
            console.log('DownloadAPI可用');

            // 调用API
            console.log('调用DownloadAPI.getDownloadRecords()...');
            const result = await DownloadAPI.getDownloadRecords();
            console.log('API原始响应:', result);

            // 处理响应
            if (!result) {
                console.warn('API返回空结果');
                return [];
            }

            if (result.success === false) {
                console.error('API明确返回失败:', result.error);
                throw new Error(result.error || 'API返回失败');
            }

            // 提取记录数组
            let records = [];
            if (Array.isArray(result.records)) {
                records = result.records;
            } else if (Array.isArray(result)) {
                records = result;
            } else if (result.data && Array.isArray(result.data.records)) {
                records = result.data.records;
            } else if (result.data && Array.isArray(result.data)) {
                records = result.data;
            }

            console.log(`成功提取 ${records.length} 条下载记录`);

            // 验证记录格式
            const validRecords = records.filter(record => {
                if (!record || typeof record !== 'object') {
                    console.warn('无效记录:', record);
                    return false;
                }
                return true;
            });

            console.log(`验证后有效记录: ${validRecords.length} 条`);
            return validRecords;

        } catch (error) {
            console.error('获取下载记录失败:', error);
            console.error('错误堆栈:', error.stack);
            throw error; // 重新抛出错误，让调用者处理
        }
    }

    /**
     * 渲染下载记录
     */
    renderDownloadRecords(records) {
        const downloadRecordsView = document.getElementById('download-records-view');

        if (!downloadRecordsView) {
            console.error('找不到下载记录视图容器');
            return;
        }

        // 确保records是数组
        if (!Array.isArray(records)) {
            console.warn('records不是数组，转换为空数组:', records);
            records = [];
        }

        console.log('开始渲染下载记录，记录数量:', records.length);

        // 创建标签页界面
        let html = `
            <div class="download-records-tabs">
                <div class="tab-header">
                    <button class="tab-btn active" data-tab="downloads">
                        <i class="fas fa-download"></i>
                        下载记录
                    </button>
                    <button class="tab-btn" data-tab="password-requests">
                        <i class="fas fa-key"></i>
                        密码申请记录
                    </button>
                </div>
                <div class="tab-content">
                    <div class="tab-panel active" id="downloads-panel">
        `;

        if (records.length === 0) {
            html += `
                        <div class="empty-state">
                            <i class="fas fa-download"></i>
                            <h3>暂无下载记录</h3>
                            <p>您还没有下载过任何文件</p>
                        </div>
            `;
        } else {
            try {
                // 按日期分组下载记录
                const groupedRecords = this.groupRecordsByDate(records);

                html += '<div class="download-records-container">';

                for (const [date, dateRecords] of Object.entries(groupedRecords)) {
                    if (Array.isArray(dateRecords) && dateRecords.length > 0) {
                        html += `
                            <div class="download-group">
                                <h3 class="download-date">${this.formatDate(date)}</h3>
                                <div class="download-items">
                        `;

                        dateRecords.forEach(record => {
                            try {
                                html += this.renderDownloadRecord(record);
                            } catch (error) {
                                console.error('渲染单个记录失败:', error, record);
                            }
                        });

                        html += `
                                </div>
                            </div>
                        `;
                    }
                }

                html += '</div>';
            } catch (error) {
                console.error('渲染下载记录时出错:', error);
                html += `
                            <div class="error-state">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h3>渲染失败</h3>
                                <p>无法显示下载记录: ${error.message}</p>
                            </div>
                `;
            }
        }

        html += `
                    </div>
                    <div class="tab-panel" id="password-requests-panel">
                        <div class="loading-state">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>加载中...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        downloadRecordsView.innerHTML = html;

        // 添加标签页切换事件
        this.initDownloadRecordsTabs();
        
        console.log(`渲染了 ${records.length} 条下载记录`);
    }

    /**
     * 渲染单个下载记录
     */
    renderDownloadRecord(record) {
        if (!record) {
            console.warn('renderDownloadRecord: record为空');
            return '';
        }

        try {
            const isEncrypted = Boolean(record.is_encrypted);
            const downloadCount = record.download_count || 0;
            const maxDownloads = 3; // 从配置获取
            const filename = record.filename || '未知文件';
            const fileSize = record.file_size || 0;
            const downloadTime = record.download_time || new Date().toISOString();
            const recordId = record.id || 'unknown';
            const fileId = record.file_id || '';

            return `
                <div class="download-record-card" data-record-id="${recordId}">
                    <div class="record-header">
                        <div class="record-icon">
                            <i class="fas fa-file-archive"></i>
                            ${isEncrypted ? '<i class="fas fa-lock encrypted-badge"></i>' : ''}
                        </div>
                        <div class="record-info">
                            <h4 class="record-filename" title="${filename}">${filename}</h4>
                            <div class="record-meta">
                                <span class="record-size">${this.formatFileSize(fileSize)}</span>
                                <span class="record-time">${this.formatTime(downloadTime)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="record-stats">
                        <div class="download-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${Math.min((downloadCount / maxDownloads) * 100, 100)}%"></div>
                            </div>
                            <span class="progress-text">${downloadCount}/${maxDownloads} 次下载</span>
                        </div>

                        ${isEncrypted ? `
                            <div class="encryption-status">
                                <i class="fas fa-shield-alt"></i>
                                <span>已加密</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="record-actions">
                        ${isEncrypted ? `
                            <button class="btn btn-primary btn-sm" onclick="fileManager.requestPasswordForRecord('${fileId}', '${filename.replace(/'/g, "\\'")}')">
                                <i class="fas fa-key"></i>
                                申请密码
                            </button>
                        ` : `
                            <button class="btn btn-secondary btn-sm" onclick="fileManager.redownloadFile('${fileId}')">
                                <i class="fas fa-download"></i>
                                重新下载
                            </button>
                        `}

                        <button class="btn btn-outline btn-sm" onclick="fileManager.showRecordDetails('${recordId}')">
                            <i class="fas fa-info-circle"></i>
                            详情
                        </button>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('渲染下载记录时出错:', error, record);
            return `
                <div class="download-record-card error">
                    <div class="record-header">
                        <div class="record-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="record-info">
                            <h4 class="record-filename">记录显示错误</h4>
                            <div class="record-meta">
                                <span class="record-size">无法显示此记录</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 按日期分组记录
     */
    groupRecordsByDate(records) {
        const groups = {};

        if (!Array.isArray(records)) {
            console.warn('groupRecordsByDate: records不是数组', records);
            return groups;
        }

        records.forEach(record => {
            try {
                if (!record || !record.download_time) {
                    console.warn('记录缺少download_time字段:', record);
                    return;
                }

                const date = new Date(record.download_time).toDateString();
                if (!groups[date]) {
                    groups[date] = [];
                }
                groups[date].push(record);
            } catch (error) {
                console.error('处理记录时出错:', error, record);
            }
        });

        return groups;
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        try {
            if (!dateString) return '未知日期';
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '无效日期';

            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            if (date.toDateString() === today.toDateString()) {
                return '今天';
            } else if (date.toDateString() === yesterday.toDateString()) {
                return '昨天';
            } else {
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
        } catch (error) {
            console.warn('格式化日期失败:', dateString, error);
            return '日期错误';
        }
    }

    /**
     * 格式化时间
     */
    formatTime(timeString) {
        try {
            if (!timeString) return '未知时间';
            const date = new Date(timeString);
            if (isNaN(date.getTime())) return '无效时间';
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.warn('格式化时间失败:', timeString, error);
            return '时间错误';
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        if (typeof bytes !== 'number') {
            bytes = parseInt(bytes) || 0;
        }
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 为记录申请密码
     */
    async requestPasswordForRecord(fileId, filename) {
        this.showPasswordRequestDialog(fileId, filename, true);
    }

    /**
     * 重新下载文件
     */
    async redownloadFile(fileId) {
        this.downloadFile(fileId);
    }

    /**
     * 显示记录详情
     */
    async showRecordDetails(recordId) {
        // TODO: 实现记录详情显示
        this.showToast('记录详情功能开发中', 'info');
    }

    /**
     * 刷新下载记录
     */
    async refreshDownloadRecords() {
        if (this.currentView === 'downloads') {
            this.showDownloadRecords();
        }
    }

    /**
     * 初始化下载记录标签页
     */
    initDownloadRecordsTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabPanels = document.querySelectorAll('.tab-panel');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const tabName = e.target.closest('.tab-btn').dataset.tab;

                // 更新标签按钮状态
                tabBtns.forEach(b => b.classList.remove('active'));
                e.target.closest('.tab-btn').classList.add('active');

                // 更新面板状态
                tabPanels.forEach(p => p.classList.remove('active'));
                const targetPanel = document.getElementById(`${tabName}-panel`);
                if (targetPanel) {
                    targetPanel.classList.add('active');
                }

                // 加载对应内容
                if (tabName === 'password-requests') {
                    await this.loadPasswordRequests();
                }
            });
        });
    }

    /**
     * 加载密码申请记录
     */
    async loadPasswordRequests() {
        const panel = document.getElementById('password-requests-panel');
        if (!panel) return;

        try {
            // 显示加载状态
            panel.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>加载密码申请记录中...</p>
                </div>
            `;

            // 获取密码申请记录
            const requests = await this.getPasswordRequests();

            // 渲染密码申请记录
            this.renderPasswordRequests(requests, panel);

        } catch (error) {
            console.error('加载密码申请记录失败:', error);
            panel.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>加载失败</h3>
                    <p>无法加载密码申请记录</p>
                    <button class="btn btn-primary" onclick="fileManager.loadPasswordRequests()">重试</button>
                </div>
            `;
        }
    }

    /**
     * 获取密码申请记录
     */
    async getPasswordRequests() {
        try {
            // 获取正确的认证token
            const authData = localStorage.getItem('fileShareAuth');
            let token = '';
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    token = auth.token || '';
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                }
            }

            // 使用正确的API调用
            const response = await fetch(`${api.getBaseURL()}/download/password-requests`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`获取密码申请记录失败: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                return result.requests || [];
            } else {
                throw new Error(result.error || '获取密码申请记录失败');
            }
        } catch (error) {
            console.error('获取密码申请记录失败:', error);
            return [];
        }
    }

    /**
     * 渲染密码申请记录
     */
    renderPasswordRequests(requests, panel) {
        if (!requests || requests.length === 0) {
            panel.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-key"></i>
                    <h3>暂无密码申请记录</h3>
                    <p>您还没有申请过任何密码</p>
                </div>
            `;
            return;
        }

        // 按状态分组
        const groupedRequests = this.groupRequestsByStatus(requests);

        let html = '<div class="password-requests-container">';

        // 渲染不同状态的申请
        const statusOrder = ['pending', 'approved', 'rejected'];
        const statusLabels = {
            'pending': { label: '待审批', icon: 'fas fa-clock', class: 'pending' },
            'approved': { label: '已批准', icon: 'fas fa-check-circle', class: 'approved' },
            'rejected': { label: '已拒绝', icon: 'fas fa-times-circle', class: 'rejected' }
        };

        statusOrder.forEach(status => {
            const statusRequests = groupedRequests[status];
            if (statusRequests && statusRequests.length > 0) {
                const statusInfo = statusLabels[status];
                html += `
                    <div class="request-group">
                        <h3 class="request-status-header ${statusInfo.class}">
                            <i class="${statusInfo.icon}"></i>
                            ${statusInfo.label} (${statusRequests.length})
                        </h3>
                        <div class="request-items">
                `;

                statusRequests.forEach(request => {
                    html += this.renderPasswordRequest(request);
                });

                html += `
                        </div>
                    </div>
                `;
            }
        });

        html += '</div>';
        panel.innerHTML = html;
    }

    /**
     * 渲染单个密码申请记录
     */
    renderPasswordRequest(request) {
        const isExpired = request.is_expired;
        const hasPassword = request.password_provided && request.status === 'approved';

        return `
            <div class="password-request-card ${request.status}" data-request-id="${request.id}">
                <div class="request-header">
                    <div class="request-icon">
                        <i class="fas fa-file-archive"></i>
                    </div>
                    <div class="request-info">
                        <h4 class="request-filename">${request.filename}</h4>
                        <div class="request-meta">
                            <span class="request-time">申请时间: ${this.formatTime(request.created_at)}</span>
                            ${request.approved_at ? `<span class="approval-time">处理时间: ${this.formatTime(request.approved_at)}</span>` : ''}
                        </div>
                    </div>
                </div>

                <div class="request-content">
                    ${request.request_reason ? `
                        <div class="request-reason">
                            <strong>申请原因:</strong> ${request.request_reason}
                        </div>
                    ` : ''}

                    ${request.approval_reason ? `
                        <div class="approval-reason">
                            <strong>处理说明:</strong> ${request.approval_reason}
                        </div>
                    ` : ''}
                </div>

                <div class="request-actions">
                    ${hasPassword && !isExpired ? `
                        <button class="btn btn-primary btn-sm" onclick="fileManager.showPassword('${request.password_provided}')">
                            <i class="fas fa-eye"></i>
                            查看密码
                        </button>
                    ` : ''}

                    ${hasPassword && isExpired ? `
                        <span class="expired-badge">
                            <i class="fas fa-clock"></i>
                            密码已过期
                        </span>
                    ` : ''}

                    ${request.status === 'pending' ? `
                        <span class="pending-badge">
                            <i class="fas fa-hourglass-half"></i>
                            等待审批
                        </span>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 按状态分组申请记录
     */
    groupRequestsByStatus(requests) {
        const groups = {};

        requests.forEach(request => {
            const status = request.status || 'pending';
            if (!groups[status]) {
                groups[status] = [];
            }
            groups[status].push(request);
        });

        return groups;
    }

    /**
     * 显示密码
     */
    showPassword(password) {
        // 创建密码显示对话框
        const dialogHtml = `
            <div class="password-display-dialog">
                <div class="dialog-header">
                    <h3>解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-display-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="password-display">
                        <label>密码:</label>
                        <div class="password-field">
                            <input type="text" class="password-input" value="${password}" readonly>
                            <button class="copy-btn" onclick="fileManager.copyPassword('${password}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="password-note">
                        <i class="fas fa-info-circle"></i>
                        <span>请妥善保管此密码，用于解压下载的文件</span>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-primary" onclick="this.closest('.password-display-dialog').remove()">
                        确定
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 自动选中密码
        const passwordInput = overlay.querySelector('.password-input');
        if (passwordInput) {
            passwordInput.select();
        }
    }

    /**
     * 复制密码到剪贴板
     */
    async copyPassword(password) {
        try {
            await navigator.clipboard.writeText(password);
            this.showToast('密码已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制密码失败:', error);
            this.showToast('复制密码失败', 'error');
        }
    }



    /**
     * 显示主页视图
     */
    showHomeView() {
        try {
            // 重置状态
            this.currentView = 'files';
            this.isShowingFavorites = false;
            
            // 更新面包屑导航
            this.updateBreadcrumb([
                { name: '首页', icon: 'fas fa-home' }
            ]);
            
            // 显示文件管理器界面元素
            this.showFileManagerElements();
            
            // 根据当前视图模式显示相应的文件视图
            const currentViewMode = this.currentViewMode || 'grid';
            if (currentViewMode === 'list') {
                this.showView('file-list');
            } else {
                this.showView('file-grid');
            }
            
            // 重新加载文件列表
            this.loadFiles();
            
            CONFIG.log('info', 'Home view displayed successfully');
            
        } catch (error) {
            console.error('显示首页视图失败:', error);
            this.showToast('显示首页视图失败', 'error');
        }
    }



    /**
     * 显示最近文件
     */
    async showRecentFiles() {
        // TODO: 实现最近文件显示
        this.updateBreadcrumb([
            { name: '首页', icon: 'fas fa-home' },
            { name: '最近访问', icon: 'fas fa-clock' }
        ]);

        this.showToast('最近访问功能开发中', 'info');
    }
}

// 创建全局文件管理器实例
let fileManager;

// 全局安全的缩略图处理函数
window.safeThumbnailLoad = function(img) {
    if (window.fileManager && window.fileManager.onThumbnailLoad) {
        window.fileManager.onThumbnailLoad(img);
    }
};

window.safeThumbnailError = function(img) {
    if (window.fileManager && window.fileManager.onThumbnailError) {
        window.fileManager.onThumbnailError(img);
    }
};

document.addEventListener('DOMContentLoaded', () => {
    // 只有在没有被app.js初始化时才创建实例
    if (!window.fileManager && !window.app) {
        fileManager = new FileManager();
        window.fileManager = fileManager;
    }
});

// 全局可用
window.FileManager = FileManager;
